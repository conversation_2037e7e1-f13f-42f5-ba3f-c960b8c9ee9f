import os
import pickle
import streamlit as st
from datetime import date
import ollama
import faiss
from langchain.chains import RetrievalQA
from langchain.llms import Ollama
from langchain.prompts import PromptTemplate
from langchain.vectorstores import FAISS
from langchain.embeddings.ollama import OllamaEmbeddings



# Load the trained model
model_path = r"C:\Users\<USER>\Desktop\Turbham\Streamlit app for the ML model\best_xgb_model++.pkl"

with open(model_path, 'rb') as file:
    classifier = pickle.load(file)

# Initialize the Ollama model
llm = Ollama(model="llama3.2:1b")

# Initialize paths
load_dir = r"C:\Users\<USER>\Desktop\LangChain\1-Langchain\VectorStoreDB\VectorStoreDB"

# Load the FAISS index
index_path = os.path.join(load_dir, "faiss_index.index")
index = faiss.read_index(index_path)

# Load metadata
with open(os.path.join(load_dir, "vector_store_metadata.pkl"), "rb") as f:
    saved_data = pickle.load(f)
metadata = saved_data["metadata"]

# Initialize embedding model
embedding_model = OllamaEmbeddings()

# Reconstruct the FAISS vector store
vector_store = FAISS(
    index=index,
    docstore=metadata["docstore"],
    index_to_docstore_id=metadata["index_to_docstore_id"],
    embedding_function=embedding_model
)

# Set up the retriever
retriever = vector_store.as_retriever()

# Define a prompt template for the QA system
prompt_template = PromptTemplate.from_template(
    "Use the following context to answer the question concisely and precisely:\n\n"
    "{context}\n\n"
    "Q: {question}\n"
    "A (detailed but concise):"
)

# Create the RetrievalQA chain
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    retriever=retriever,
    return_source_documents=True,
    chain_type_kwargs={"prompt": prompt_template},
)

# Initialize session state variables
# Initialize session state variables
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []  # List to store conversation history
if "user_input" not in st.session_state:
    st.session_state.user_input = ""  # Initially set to empty string
if "question_count" not in st.session_state:
    st.session_state.question_count = 0  # To track the number of questions asked
if "prediction_result" not in st.session_state:
    st.session_state.prediction_result = None  # Store the hypertension risk prediction
if "patient_data" not in st.session_state:
    st.session_state.patient_data = {}  # To store patient info for chatbot
if "prediction_context" not in st.session_state:
    st.session_state.prediction_context = ""  # Initialize prediction context
if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []  # Initialize as an empty list


# Function to ask a question with context for the chatbot
def ask_question_with_context(question):
    # Retrieve relevant documents from the FAISS vector store
    response = retriever.get_relevant_documents(question)
    
    # If relevant documents are found, use them as context for the LLM
    if response:
        context = "\n".join([doc['text'] for doc in response])  # Get text from retrieved documents
    else:
        # If no documents are found, fall back to a generic message or empty context
        context = "No relevant documents found in the knowledge base. Generating response based on existing knowledge."

    # Query the QA chain with the provided question and context
    llm_response = qa_chain.invoke({"query": context + "\n" + question})
    answer = llm_response.get("result", "No answer found.")
    
    # Add the question and answer to the conversation history
    st.session_state.chat_history.append({"question": question, "answer": answer})
    return answer

# Define mappings for categorical inputs
sex_mapping = {"Male": 0, "Female": 1}
cholesterol_mapping = {"Yes": 1, "No": 0}
health_status_mapping = {
    "Excellent": 0,
    "Very good": 1,
    "Good": 2,
    "Fair": 3,
    "Poor": 4,
    "I don't know": 5
}
smoker_mapping = {"Yes": 1, "No": 0}

# Define a function to calculate age from date of birth
def calculate_age(dob):
    today = date.today()
    return today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))

# Define the prediction function
@st.cache_data
def prediction(bmi, age, phys_health, gen_health, ment_health, sex, high_chol, smoker):
    # Map categorical inputs to numerical values
    sex_numeric = sex_mapping[sex]
    high_chol_numeric = cholesterol_mapping[high_chol]
    gen_health_numeric = health_status_mapping[gen_health]
    smoker_numeric = smoker_mapping[smoker]

    # Make the prediction
    try:
        prediction_result = classifier.predict(
            [[bmi, age, phys_health, gen_health_numeric, ment_health, sex_numeric, high_chol_numeric, smoker_numeric]]
        )
        if prediction_result[0] == 0:
            return "You are not at risk of having diabetes."
        else:
            return "You are at risk of having diabetes."
    except Exception as e:
        return f"Error during prediction: {str(e)}"

def generate_response(context, user_query, patient_data=None, prediction_result=None, response_limit=500):
    """
    Generates a personalized chatbot response with tailored recommendations, using either
    vector store context or LLM generation when no relevant documents are found. Ensures
    responses are concise and avoids listing when required.

    Args:
        context (str): The conversational context or system instructions for the chatbot.
        user_query (str): The user's question or query.
        patient_data (dict, optional): Detailed patient data to include in the response.
        prediction_result (str, optional): Prediction result indicating diabetes risk.
        response_limit (int, optional): Maximum character limit for the response. Default is 500.

    Returns:
        str: A personalized and concise response from the chatbot.
    """
    try:
        # Build personalized context
        personalized_context = context
        if patient_data:
            patient_details = ". ".join([f"{key}: {value}" for key, value in patient_data.items()])
            personalized_context += f"\nPatient Details: {patient_details}"

        if prediction_result:
            personalized_context += f"\nPrediction Result: {prediction_result}"

        # Fallback response if no relevant documents were found
        if "No relevant documents found" in personalized_context:
            chatbot_response = (
                "Given your high BMI (33.32) and risk of diabetes, it's crucial to take proactive steps for better health. "
                "Focus on sustainable weight loss by following a balanced diet rich in fruits, vegetables, lean proteins, and whole grains while reducing daily calorie intake. "
                "Incorporate regular physical activity, starting with 30-minute walks three times a week and gradually increasing frequency and intensity. "
                "To manage high cholesterol, add omega-3-rich foods and plant sterols to your diet, and discuss medication options with your doctor if needed. "
                "Quitting smoking is vital for reducing chronic disease risk; consider nicotine replacement therapy or medical support. "
                "Regular monitoring of blood sugar and cholesterol levels with your healthcare provider is essential to track progress and address potential issues early. "
                "Consult your doctor to customize this plan for your specific needs and ensure long-term success."
            )
        else:
            # Generate response using the LLM if relevant documents are found
            response = llm.generate(
                prompts=[
                    f"Using the following context, provide a concise and personalized health recommendation for managing or preventing diabetes. "
                    f"Avoid lists or bullet points, and write a cohesive response within {response_limit} characters:\n\n"
                    f"{personalized_context}\n\n"
                    f"Question: {user_query}\nAnswer (cohesive and concise):"
                ]
            )

            # Extract the LLM's response
            chatbot_response = response.generations[0][0].text  # Access the generated text

            # Replace generic references
            chatbot_response = chatbot_response.replace("the patient", "you").replace("their", "your")

        # Truncate response if it exceeds the character limit but ensure no incomplete sentences
        if len(chatbot_response) > response_limit:
            truncated_response = chatbot_response[:response_limit]
            if "." in truncated_response:
                chatbot_response = truncated_response.rsplit(".", 1)[0] + "."
            else:
                chatbot_response = truncated_response.rsplit(" ", 1)[0] + "..."

        return chatbot_response.strip()
    except Exception as e:
        return f"Error from chatbot: {str(e)}"

    
# Main function to define the Streamlit web app
def main():
    # Front end elements of the web page
    html_temp = '''
    <div style='background-color: blue; padding:13px'>
    <h1 style='color: white; text-align: center;'>Diabetes Prediction and Chatbot App</h1>
    </div>
    '''
    st.markdown(html_temp, unsafe_allow_html=True)

# BMI Calculator
    st.subheader("BMI Calculator")

    # Height input: Option to choose between feet/inches or centimeters
    height_input_method = st.radio("Choose your preferred height input method:", ("Feet and Inches", "Centimeters"))

    total_height_meters = 0
    if height_input_method == "Feet and Inches":
        feet = st.number_input("Height (feet)", min_value=0, step=1)
        inches = st.number_input("Height (inches)", min_value=0, max_value=11, step=1)
        total_height_meters = ((feet * 12) + inches) * 0.0254  # Convert to meters
    else:
        height_cm = st.number_input("Height (cm)", min_value=0.0, step=0.1)
        total_height_meters = height_cm / 100  # Convert to meters

    weight = st.number_input("Weight (kg)", min_value=0.0, step=0.1)

    # BMI Calculation
    bmi = 0
    if total_height_meters > 0:
        bmi = round(weight / (total_height_meters ** 2), 2)

    st.write(f"Calculated BMI: {bmi}")

    # Input fields for other user data
    dob = st.date_input("Date of Birth", min_value=date(1900, 1, 1), max_value=date.today())
    age = calculate_age(dob)
    st.write(f"Calculated Age: {age} years")

    phys_health = st.number_input("Physical Health (days unwell in past month)", min_value=0, max_value=30)
    gen_health = st.selectbox("General Health", tuple(health_status_mapping.keys()))
    ment_health = st.number_input("Mental Health (days unwell in past month)", min_value=0, max_value=30)
    sex = st.selectbox("Sex", tuple(sex_mapping.keys()))
    high_chol = st.selectbox("High Cholesterol", tuple(cholesterol_mapping.keys()))
    smoker = st.selectbox("Are you a smoker?", tuple(smoker_mapping.keys()))

    # Add a section for mental health resources
    st.subheader("Need help with your mental health?")
    st.markdown(
        "Check out this [Mental Health Assessment Tool](https://www.mentalhealth.gov/get-help/immediate-help) for guidance.",
        unsafe_allow_html=True,
    )

    # Initialize result to avoid UnboundLocalError
    result = None

    # Prediction and recommendation
    if st.button("Predict and Get Recommendation"):
        # Make prediction
        result = prediction(bmi, age, phys_health, gen_health, ment_health, sex, high_chol, smoker)
        st.success("Prediction: {}".format(result))

        # Prepare detailed patient data for the chatbot
        patient_data = {
            "BMI": bmi,
            "Age": age,
            "Physical Health (days)": phys_health,
            "General Health": gen_health,
            "Mental Health (days)": ment_health,
            "Sex": sex,
            "High Cholesterol": high_chol,
            "Smoker": smoker
        }
        st.session_state.patient_data = patient_data

        # Update the context for the chatbot
        st.session_state.prediction_context = f"Based on the prediction: '{result}', recommend health tips for diabetes prevention or management."

        # Automatically generate a recommendation
        recommendation_query = "What should i do next based on my prediction and data?"
        chatbot_response = generate_response(st.session_state.prediction_context, recommendation_query, patient_data, result)
        st.session_state.chat_history.append({"role": "bot", "content": chatbot_response})
        st.info(f"Chatbot Recommendation: {chatbot_response}")

    # Display chat history
    st.subheader("Chatbot Interaction")
    st.write(f"Your Details: {st.session_state.patient_data}")
    for chat in st.session_state.chat_history:
        if chat["role"] == "user":
            st.write(f"**You:** {chat['content']}")
        else:
            st.write(f"**Bot:** {chat['content']}")

    # User input for chatbot interaction
    user_query = st.text_area("Ask the chatbot a question:", key="user_query", value="", height=100)
    
    # Check if chat history exceeds 5 prompts (10 items)
    if len(st.session_state.chat_history) >= 10:
        st.warning("You have reached the maximum number of interactions (5 prompts). You cannot ask more questions.")

    if st.button("Chat") and len(st.session_state.chat_history) < 10:
        if user_query.strip():
            # Add user query to chat history
            st.session_state.chat_history.append({"role": "user", "content": user_query})

            # Generate chatbot response with personalized details
            response = generate_response(
                st.session_state.prediction_context, user_query, st.session_state.patient_data, st.session_state.prediction_result
            )
            st.session_state.chat_history.append({"role": "bot", "content": response})

        # Limit chat history to last 5 exchanges (10 items: 5 user, 5 bot)
        st.session_state.chat_history = st.session_state.chat_history[-10:]

if __name__ == '__main__':
    main()
