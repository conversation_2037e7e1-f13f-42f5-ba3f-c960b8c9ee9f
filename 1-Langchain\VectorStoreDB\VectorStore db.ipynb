##building a sample vectordb
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import TextLoader
from langchain_ollama import OllamaEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter


loader = TextLoader("cleaned_text.txt")
data = loader.load()
data

text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
docs = text_splitter.split_documents(data)
docs

## Using the default model
embeddings=(
    OllamaEmbeddings(model="qwen2.5:1.5b")
)
embeddings

from langchain.embeddings import OllamaEmbeddings

# Load the embedding model
embedding_model = OllamaEmbeddings(model="qwen2.5:1.5b")

# Embed the text chunks
text_chunks = [docs] 
embedded_texts = embedding_model.embed_documents(text_chunks)
embedded_texts


import faiss
import pickle
import os
import numpy as np
from langchain_community.vectorstores import FAISS
from langchain.docstore import InMemoryDocstore
from langchain_community.embeddings import OllamaEmbeddings

# Example: Ensure embedded_texts is a valid list of float vectors
embedded_texts = np.array(embedded_texts, dtype="float32")

# Create FAISS index
dimension = embedded_texts.shape[1]
index = faiss.IndexFlatL2(dimension)  # L2 distance for similarity search
index.add(embedded_texts)  # Add embeddings to the FAISS index

docstore = InMemoryDocstore({str(i): doc for i, doc in enumerate(docs)})
index_to_docstore_id = {i: str(i) for i in range(len(docs))} 

# Define save directory
save_dir = r"C:\Users\<USER>\OneDrive\Desktop\Langchain\1-Langchain\VectorStoreDB"
os.makedirs(save_dir, exist_ok=True)

# Save FAISS index
index_path = os.path.join(save_dir, "index.faiss")
faiss.write_index(index, index_path)

# Save metadata (docstore and index_to_docstore_id)
metadata_path = os.path.join(save_dir, "index.pkl")
metadata = {"docstore": {}, "index_to_docstore_id": {}}  # Initialize empty metadata for now
with open(metadata_path, "wb") as f:
    pickle.dump(metadata, f)

print("✅ FAISS index and metadata saved successfully!")


# Load FAISS index
index = faiss.read_index(index_path)

# Load metadata
with open(metadata_path, "rb") as f:
    saved_data = pickle.load(f)

# Reconstruct FAISS vector store
embedding_function = OllamaEmbeddings(model="qwen2.5:1.5b")  # Ensure correct embedding function

vector_store = FAISS(
    index=index,
    docstore=saved_data.get("docstore", {}),  # Use empty dict if missing
    index_to_docstore_id=saved_data.get("index_to_docstore_id", {}),
    embedding_function=embedding_function
)

print("✅ FAISS index and metadata loaded successfully!")


retriever = vector_store.as_retriever(search_type="similarity")
retriever

from langchain_ollama import OllamaLLM

# Initialize the LLM
llm = OllamaLLM(model="qwen2.5:1.5b")

# Test the LLM with a simple query
response = llm.invoke("What are the common symtoms of diabetes?")
print("LLM Response:", response)



from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.llms import Ollama

# Initialize the Ollama model
llm = OllamaLLM(model="qwen2.5:1.5b", temperature=0.5)


# Set up the retriever
retriever = vector_store.as_retriever(search_kwargs={"k": 5})  # Retrieve only the top 3 relevant chunks


# Define a prompt template for the QA system
prompt_template = PromptTemplate.from_template(
    "Use the following context to answer the question concisely and precisely:\n\n"
    "{context}\n\n"
    "Q: {question}\n"
    "A (detailed but concise):"
)


# Create the RetrievalQA chain
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    retriever=retriever,
    return_source_documents=True,  # To get source documents for debugging
    chain_type_kwargs={"prompt": prompt_template},
)

def ask_question(question):
    response = qa_chain.invoke({"query": question})
    answer = response.get("result", "No answer found.")
    

    # Post-process the response to enforce brevity
    max_words = 500  # Set a word limit for the response
    answer = " ".join(answer.split()[:max_words]) + ("..." if len(answer.split()) > max_words else "")

    return answer


# Example usage
question = "what are the types of diabetes?"
answer = ask_question(question)

print("Question:", question)
print("\nAnswer:", answer)

# Example usage
question = "Is diabetes a chronic disease?" "Answer should be taken from the vector store"
answer = ask_question(question)

print("Question:", question)
print("\nAnswer:", answer)

from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.llms import Ollama

# Initialize the Ollama model
llm = Ollama(model="qwen2.5:1.5b", temperature=0.5)

# Set up the retriever
retriever = vector_store.as_retriever(search_kwargs={"k": 5})  # Retrieve only the top 5 relevant chunks

# Define a prompt template for the QA system
prompt_template = PromptTemplate.from_template(
    "Use the following context to answer the question concisely and precisely:\n\n"
    "{context}\n\n"
    "Q: {question}\n"
    "A (detailed but concise):"
)

# Create the RetrievalQA chain
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    retriever=retriever,
    return_source_documents=True,  # Include source documents for debugging or references
    chain_type_kwargs={"prompt": prompt_template},
)

def ask_question(question):
    # Retrieve the top documents from the vector store
    retrieved_docs = retriever.invoke(question)
    context = " ".join([doc.page_content for doc in retrieved_docs])

    # Use the `query` key as required by the RetrievalQA chain
    response = qa_chain.invoke({"query": question})

    # Extract the answer from the response
    answer = response.get("result", "No answer found.")

    # Post-process the response to enforce brevity
    max_words = 500  # Set a word limit for the response
    answer = " ".join(answer.split()[:max_words]) + ("..." if len(answer.split()) > max_words else "")

    return answer


# Example usage
question = "Is diabetes a chronic disease?" 
answer = ask_question(question)

print("Question:", question)
print("\nAnswer:", answer)

import faiss
import os
import pickle
from langchain.embeddings.ollama import OllamaEmbeddings

# Initialize the OllamaEmbeddings object
embedding_model = OllamaEmbeddings()

# Directory to save the index and metadata
save_dir = "VectorStoreDB"
os.makedirs(save_dir, exist_ok=True)  # Ensure the directory exists

# Save the FAISS index
faiss.write_index(vector_store.index, os.path.join(save_dir, "faiss_index.index"))

# Save metadata
metadata = {
    "docstore": vector_store.docstore,
    "index_to_docstore_id": vector_store.index_to_docstore_id,
}

# Save the metadata and embedding model configuration
with open(os.path.join(save_dir, "vector_store_metadata.pkl"), "wb") as f:
    pickle.dump({
        "metadata": metadata,
        "embedding_model": "OllamaEmbeddings",  # Save the embedding model's class or identifier
    }, f)


import faiss
import os
import pickle
from langchain.embeddings.ollama import OllamaEmbeddings
from langchain.vectorstores import FAISS

# Directory where the index and metadata are saved
load_dir = "VectorStoreDB"

# Load the FAISS index
index_path = os.path.join(load_dir, "faiss_index.index")
index = faiss.read_index(index_path)

# Load metadata
with open(os.path.join(load_dir, "vector_store_metadata.pkl"), "rb") as f:
    saved_data = pickle.load(f)
metadata = saved_data["metadata"]

# Initialize the OllamaEmbeddings object (ensure it matches what was used during saving)
embedding_model = OllamaEmbeddings()

# Reconstruct the FAISS vector store
vector_store = FAISS(
    index=index,
    docstore=metadata["docstore"],
    index_to_docstore_id=metadata["index_to_docstore_id"],
    embedding_function=embedding_model  # Pass the OllamaEmbeddings object
)
