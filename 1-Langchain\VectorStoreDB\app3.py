import os
import pickle
import ollama
import faiss
import numpy as np
import streamlit as st
from datetime import date
from langchain_ollama import ChatOllama
from langchain_core.output_parsers import StrOutputParser
from langchain.chains import RetrievalQA
from langchain.llms import Ollama
from langchain.prompts import PromptTemplate
from langchain.vectorstores import FAISS
from langchain.embeddings.ollama import OllamaEmbeddings
from langchain.docstore import InMemoryDocstore

from langchain_core.prompts import (
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate,
    AIMessagePromptTemplate,
    ChatPromptTemplate
)

# Sidebar configuration
with st.sidebar:
    st.header("⚙️ Configuration")
    selected_model = st.selectbox(
        "Choose Model",
        ["deepseek-r1:1.5b", "llama3.2:1b"],
        index=0
    )
    embedding_model = OllamaEmbeddings(model=selected_model)

# initiate the chat engine

llm_engine=ChatOllama(
    model=selected_model,
    base_url="http://localhost:11434",

    temperature=0.3

)

# Initialize paths
load_dir = r"C:\Users\<USER>\Desktop\LangChain\1-Langchain\VectorStoreDB"

# Load FAISS index
index_path = os.path.join(load_dir, "vector_index.faiss")
metadata_path = os.path.join(load_dir, "vector_store.pkl")

# Modify the FAISS loading section like this:

# Load FAISS index with proper error handling
if not os.path.exists(index_path) or not os.path.exists(metadata_path):
    st.error("🔴 FAISS index or metadata file not found. Please check the directory.")
else:
    # Load FAISS index
    index = faiss.read_index(index_path)

    # Load stored data
    with open(metadata_path, "rb") as f:
        saved_data = pickle.load(f)

    # Extract necessary components
    docstore_data = saved_data.get("docstore", {})
    index_to_docstore_id = saved_data.get("index_to_docstore_id", {})

    # Ensure valid docstore
    docstore = InMemoryDocstore(docstore_data if isinstance(docstore_data, dict) else {})

    # Fix missing IDs if needed
    for i in range(index.ntotal):
        if i not in index_to_docstore_id:
            index_to_docstore_id[i] = str(i)

    # Reconstruct FAISS vector store
    vector_store = FAISS(
        index=index, 
        docstore=docstore, 
        index_to_docstore_id=index_to_docstore_id, 
        embedding_function=embedding_model
    )

    # Initialize retriever
    retriever = vector_store.as_retriever()


# System prompt configuration
system_prompt = SystemMessagePromptTemplate.from_template(
    "You are an expert AI medical assistant. Provide concise, correct recommendations"
    "with strategic plans for healthcare issues. Always respond in English."
)

# Create the RetrievalQA chain
qa_chain = RetrievalQA.from_chain_type(
    llm=llm_engine,
    retriever=retriever,
    return_source_documents=True,
    chain_type_kwargs={"prompt": prompt_template},
)

# Session state management
if "message_log" not in st.session_state:
    st.session_state.message_log = [{"role": "ai", "content": "Hi! I'm Dr Deuces. How can I help you today?"}]

# Initialize session state variables
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []  # List to store conversation history
if "user_input" not in st.session_state:
    st.session_state.user_input = ""  # Initially set to empty string
if "question_count" not in st.session_state:
    st.session_state.question_count = 0  # To track the number of questions asked
if "prediction_result" not in st.session_state:
    st.session_state.prediction_result = None  # Store the hypertension risk prediction
if "patient_data" not in st.session_state:
    st.session_state.patient_data = {}  # To store patient info for chatbot
if "prediction_context" not in st.session_state:
    st.session_state.prediction_context = ""  # Initialize prediction context
if 'conversation_history' not in st.session_state:
    st.session_state.conversation_history = []  # Initialize as an empty list
if "messages" not in st.session_state:
    st.session_state.messages = []
if "user_query_submitted" not in st.session_state:
    st.session_state.user_query_submitted = False
if "page_index" not in st.session_state:
    st.session_state.page_index = 0
if "user_name" not in st.session_state:
    st.session_state.user_name = "User"
if "total_risk_score" not in st.session_state:
    st.session_state.total_risk_score = None
if "risk_level" not in st.session_state:
    st.session_state.risk_level = None
if "risk_description" not in st.session_state:
    st.session_state.risk_description = None
if "first_question" not in st.session_state:
    st.session_state.first_question = True 

# Function to ask a question with context for the chatbot
def ask_question_with_context(question):
    # Retrieve relevant documents from the FAISS vector store
    response = retriever.get_relevant_documents(question)
    
    # If relevant documents are found, use them as context for the LLM
    if response:
        context = "\n".join([doc['text'] for doc in response])  # Get text from retrieved documents
    else:
        # If no documents are found, fall back to a generic message or empty context
        context = "No relevant documents found in the knowledge base. Generating response based on existing knowledge."

    # Query the QA chain with the provided question and context
    llm_response = qa_chain.invoke({"query": context + "\n" + question})
    answer = llm_response.get("result", "No answer found.")
    
    # Add the question and answer to the conversation history
    st.session_state.chat_history.append({"question": question, "answer": answer})
    return answer

# Mappings for risk factors
sex_mapping = {"Male": 0, "Female": 1}
cholesterol_levels_mapping = {"high": 2, "low": 0, "I don't know": 1 }
diet_mapping = {
    "Mostly Healthy (Fruits, Vegetables, Whole Grains)": 0,
    "Moderately Healthy (Mix of healthy and Processed foods)": 2,
    "Mostly Unhealthy (Processed Foods and Sugary Drinks 1–2 times a week)": 4,
    "Very Unhealthy (Processed Foods and Sugary Drinks 3–5 times a week)": 6,
    "Unsure": 2,
    "I don't know": 1
}
physical_activity_mapping = {
    "Sedentary (Rarely or Never)": 2,
    "Lightly active (1-2x/wk)": 4,
    "Moderately active (3-4x/ Wk)": 2,
    "Very active (5 -7  X/wk)": 0,
    "Extremely active (athlete-level activity)": -2,
    "I don't know": 1
}
stress_mapping = {
    "Low": 0,
    "Moderate": 2,
    "High": 4,
    "Very High": 6,
    "I don't know": 1
}
family_history_mapping = {
    "No family history": 0,
    "One parent with diabetes": 4,
    "Both parents with diabetes": 8,
    "Sibling(s) with diabetes": 6,
    "Other relatives with diabetes": 2,
    "I don't know": 1
}
smoking_alcohol_mapping = {
    "Non-smoker, non-drinker": 0,
    "Occasional smoker or drinker": 3,
    "Regular smoker or drinker": 4,
    "Heavy smoker or drinker": 6,
    "I don't know": 1
}
gest_diabetes_mapping = {
    "Not Applicable": 0,
    "No": 0,
    "Yes": 5,
    "Unsure": 2,
    "I don't know": 1
}
pre_diabetes_mapping = {
    "No": 0,
    "Yes (One time)": 3,
    "Yes (Multiple times)": 5,
    "Never checked": 2,
    "I don't know": 1
}
pre_conditions_mapping = {
    "None": 0,
    "Hypertension": 4,
    "High cholesterol/triglycerides": 4,
    "Cardiovascular disease": 5,
    "Multiple": 8,
    "I don't know": 2
}
sleep_mapping = {
    "Less than 5 hours": 6,
    "5-6 hours": 3,
    "7-8 hours": 0,
    "More than 8 hours": 1,
}
medications_mapping = {
    "None": 0,
    "Blood Pressure Medication": 3,
    "Steroids": 3,
    "Antipsychotics": 4,
    "Multiple Medications": 6,
    "I don't know": 2
}

waist_circumference_mapping = {
    "Below 80 cm (women) / 90 cm (men)": 0,
    "80–88 cm (women) / 90–102 cm (men)": 2,
    "Above 88 cm (women) / 102 cm (men)": 5,
    "I don’t know": 2
}


def calculate_age(dob):
    today = date.today()
    return today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))

def convert_spo2(val):
    """
    Convert 'SpO2Levels' values to risk points.
    If the value is numeric, then:
       - If SpO2 is 95 or above, return 0 points (normal).
       - If below 95, return 2 points (low).
    If conversion fails, map known strings to risk points.
    """
    try:
        spo2 = float(val)
        return 0 if spo2 >= 95 else 2
    except (ValueError, TypeError):
        # New mapping for non-numeric inputs:
        mapping = {
            "Normal (≥95%)": 0,
            "Low (<95%)": 2,
            "I don't know": 1
        }
        return mapping.get(val, 0)

def convert_blood_glucose(val):
    """
    Convert 'BloodGlycose' to a float.
    If non-numeric, map known strings to a float value.
    """
    try:
        return float(val)
    except (ValueError, TypeError):
        mapping = {
            "Prediabetes": 110.0,
            "Diabetes": 150.0,
            "Normal": 90.0
        }
        return mapping.get(val, 0.0)

def blood_glucose_points(val):
    """
    Convert the blood glucose value to risk points.
    Thresholds:
      - <100 mg/dL: 0 points (normal)
      - 100-125 mg/dL: 2 points (prediabetic range)
      - ≥126 mg/dL: 4 points (diabetic range)
    """
    bg = convert_blood_glucose(val)
    if bg < 100:
        return 0
    elif bg < 126:
        return 2
    else:
        return 4

def convert_fbs(val):
    """
    Convert 'FBS' (Fasting Blood Sugar) to a boolean.
    For numeric values, 0 is False and nonzero is True.
    For non-numeric, map known values.
    """
    try:
        num_val = float(val)
        return bool(num_val)
    except (ValueError, TypeError):
        mapping = {
            "Yes": True,
            "No": False,
            "True": True,
            "False": False
        }
        return mapping.get(val, False)

def fbs_points(val):
    """
    Convert FBS to risk points.
    For example, if FBS is True (indicating high fasting sugar), assign 3 points;
    if False, 0 points.
    """
    return 3 if convert_fbs(val) else 0

def convert_to_float(val):
    """
    Convert a value to float.
    If conversion fails, map known strings to representative float values.
    """
    try:
        return float(val)
    except (ValueError, TypeError):
        mapping = {
            "High": 6.0,
            "Normal": 5.0,
            "Low": 4.0
        }
        return mapping.get(val, 0.0)

def hba1c_points(val):
    """
    Convert HbA1c value to risk points.
    Thresholds:
      - <5.7%: 0 points (normal)
      - 5.7% to 6.4%: 2 points (prediabetic range)
      - ≥6.5%: 4 points (diabetic range)
    """
    hba1c = convert_to_float(val)
    if hba1c < 5.7:
        return 0
    elif hba1c < 6.5:
        return 2
    else:
        return 4

def ppg_points(val):
    """
    Convert PPG (Post Prandial Glucose) value to risk points.
    Thresholds:
      - <140 mg/dL: 0 points (normal)
      - 140-199 mg/dL: 2 points (impaired)
      - ≥200 mg/dL: 4 points (diabetic)
    """
    ppg = convert_to_float(val)
    if ppg < 140:
        return 0
    elif ppg < 200:
        return 2
    else:
        return 4

def calculate_bp_points(systolic, diastolic):
    if systolic < 120 and diastolic < 80:
        return 0
    elif 120 <= systolic <= 129 and diastolic < 80:
        return 2
    elif 130 <= systolic <= 139 and 80 <= diastolic <= 89:
        return 4
    elif systolic >= 140 or diastolic >= 90:
        return 8
    return 3  # For unknown blood pressure

def get_risk_level(total_score):
    """
    Determine the risk level and description based on the total risk score.
    """
    if total_score <= 20:
        return "Low Risk", "Your risk of developing diabetes is low. Would you like to discuss this with the Chat Assistant?"
    elif 21 <= total_score <= 40:
        return "Moderate Risk", "You have a moderate risk of developing diabetes. Would you like to discuss this with the Chat Assistant?"
    elif 41 <= total_score <= 60:
        return "High Risk", "You have a high risk of developing diabetes. Would you like to discuss this with the Chat Assistant?"
    else:  # total_score > 60
        return "Very High Risk", "Your risk of developing diabetes is very high. Would you like to discuss this with the Chat Assistant?"


def calculate_risk_score(input_values):
    """
    Calculate the total risk score by summing risk points for:
      - Clinical measures (blood pressure via calculate_bp_points)
      - SpO2Levels (via convert_spo2)
      - Other mapped risk factors (using existing mappings)
      - Newly converted parameters:
            BloodGlycose, FBS, HbA1c, PPG
    """
    bp_score = calculate_bp_points(input_values['Systolic'], input_values['Diastolic'])
    scores = [
        bp_score,
        convert_spo2(input_values['SpO2Levels']),
        family_history_mapping[input_values['FamilyHistory']],
        gest_diabetes_mapping[input_values['GestationalDiabetes']],
        pre_diabetes_mapping[input_values['PreDiabetes']],
        pre_conditions_mapping[input_values['PreExistingConditions']],
        diet_mapping[input_values['Diet']],
        physical_activity_mapping[input_values['PhysicalActivity']],
        sleep_mapping[input_values['SleepHours']],
        stress_mapping[input_values['StressLevels']],
        smoking_alcohol_mapping[input_values['SmokingAlcohol']],
        medications_mapping[input_values['Medications']],
        waist_circumference_mapping[input_values['WaistCircumferenceCM']],
        blood_glucose_points(input_values['BloodGlycose']),
        fbs_points(input_values['FBS']),
        hba1c_points(input_values['HbA1c']),
        ppg_points(input_values['PPG'])
    ]
    
    total_score = sum(scores)
    return total_score

def main():
    html_temp = '''
    <div style='background-color: blue; padding:13px'>
    <h1 style='color: white; text-align: center;'>Diabetes Risk Prediction App</h1>
    </div>
    '''
    st.markdown(html_temp, unsafe_allow_html=True)
    
    # --- Page Content ---
    st.subheader("Personal Information")  
    st.write("***This section collects basic personal details, including age and sex, which are important for assessing diabetes risk.***")  
    dob = st.date_input("Enter your Date of Birth", min_value=date(1900, 1, 1), max_value=date.today())  
    age = calculate_age(dob)  
    st.write(f"Calculated Age: {age} years")  
    sex = st.selectbox("What is your Gender?", list(sex_mapping.keys()), index=None)  

    st.subheader("Clinical Parameters")  
    st.write("***These are key health indicators that help assess your current metabolic and cardiovascular status.***")  
    raw_fbs = st.number_input("Fasting Blood Sugar (mg/dL)(What is your Blood Sugar Level before eating?)", 0.0, 400.0, value=None)  
    raw_ppg = st.number_input("Post Prandial Glucose (mg/dL)(What is your Blood Sugar Level after eating?)", 0.0, 400.0, value=None)  
    raw_hbA1c = st.number_input("HbA1c (0%-15%)(Average blood sugar level over the past 2-3 months)", 0.0, 15.0, value=None)  
    raw_blood_glycose = st.number_input("Blood Glycose (mg/dL)", 0.0, 400.0, value=None)   
    systolic = st.number_input("Systolic Blood Pressure (mmHg)", 0.0, 200.0, value=None)  
    diastolic = st.number_input("Diastolic Blood Pressure (mmHg)", 0.0, 130.0, value=None)  
    raw_spO2 = st.selectbox("How do you rate your SpO2 level?", ["Normal (≥95%)", "Low (<95%)", "I don't know"], index=None)     
    chol = st.selectbox("How would you describe your cholesterol level?", list(cholesterol_levels_mapping.keys()), index=None) 

    fbs = convert_fbs(raw_fbs)
    ppg = convert_to_float(raw_ppg)
    hbA1c = convert_to_float(raw_hbA1c)
    blood_glycose = convert_blood_glucose(raw_blood_glycose)
    spO2 = convert_spo2(raw_spO2)

    st.subheader("Family and Medical History")  
    st.write("***This section gathers information on genetic and medical risk factors that can contribute to diabetes.***")  
    family_history = st.selectbox("Which of your first-degree relatives have a history of diabetes?", list(family_history_mapping.keys()), index=None)  
    gest_diabetes = st.selectbox("Do you have any history of gestational diabetes?", list(gest_diabetes_mapping.keys()), index=None)  
    pre_diabetes = st.selectbox("Have you ever been told you have high blood sugar?", list(pre_diabetes_mapping.keys()), index=None)  
    pre_conditions = st.selectbox("Do you have any pre-existing medical conditions?", list(pre_conditions_mapping.keys()), index=None)    
    medications = st.selectbox("Are you taking any of these medications?", list(medications_mapping.keys()), index=None)  

    st.subheader("Lifestyle Factors")  
    st.write("***Your daily habits, including diet, exercise, and stress levels, play a crucial role in diabetes risk.***")  
    diet = st.selectbox("How would you describe your diet?", list(diet_mapping.keys()), index=None)  
    physical_activity = st.selectbox("How often do you exercise?", list(physical_activity_mapping.keys()), index=None)  
    sleep_hours = st.selectbox("How many hours would you usually sleep at night?", list(sleep_mapping.keys()), index=None)  
    stress = st.selectbox("Over the past month, how would you rate your stress level?", list(stress_mapping.keys()), index=None)  
    smoking_alcohol = st.selectbox("How would you describe your smoking and alcohol habits?", list(smoking_alcohol_mapping.keys()), index=None)  

    st.subheader("Biometric Data")  
    st.write("***Body measurements like waist circumference are important indicators of obesity-related diabetes risk.***")  
    waist_circumference = st.selectbox("Select your waist circumference:", list(waist_circumference_mapping.keys()), index=None)  

    st.markdown("**Disclaimer:** Any missing fields will be taken as the 'I don't know' option by default.")  


    # Define the total number of inputs to track progress
    total_fields = 21  # Adjust based on the number of inputs you want to track
    filled_fields = 0  # Initialize filled fields count

    # Check if each field has been filled (replace with actual field conditions)
    filled_fields += int(dob is not None)
    filled_fields += int(sex is not None)
    filled_fields += int(fbs is not None)
    filled_fields += int(ppg is not None)
    filled_fields += int(hbA1c is not None)
    filled_fields += int(systolic is not None)
    filled_fields += int(diastolic is not None)
    filled_fields += int(spO2 is not None)
    filled_fields += int(blood_glycose is not None)
    filled_fields += int(chol is not None)
    filled_fields += int(family_history is not None)
    filled_fields += int(gest_diabetes is not None)
    filled_fields += int(pre_diabetes is not None)
    filled_fields += int(pre_conditions is not None)
    filled_fields += int(medications is not None)
    filled_fields += int(diet is not None)
    filled_fields += int(physical_activity is not None)
    filled_fields += int(sleep_hours is not None)
    filled_fields += int(stress is not None)
    filled_fields += int(smoking_alcohol is not None)
    filled_fields += int(waist_circumference is not None)

    # Calculate progress percentage
    progress = filled_fields / total_fields

    # Display progress bar
    st.sidebar.subheader("Progress Bar")
    st.sidebar.progress(progress)
    st.sidebar.write(f"Form Completion: {int(progress * 100)}%")


    prediction_button = st.button("Predict Risk", type="primary")

    if prediction_button:
        st.session_state.patient_data["dob"] = dob
        st.session_state.patient_data["age"] = age
        st.session_state.patient_data["sex"] = sex
        st.session_state.patient_data["fbs"] = fbs
        st.session_state.patient_data["ppg"] = ppg
        st.session_state.patient_data["hbA1c"] = hbA1c
        st.session_state.patient_data["systolic"] = systolic
        st.session_state.patient_data["diastolic"] = diastolic
        st.session_state.patient_data["spO2"] = spO2
        st.session_state.patient_data["chol"] = chol
        st.session_state.patient_data["blood_glycose"] = blood_glycose
        st.session_state.patient_data["family_history"] = family_history
        st.session_state.patient_data["gest_diabetes"] = gest_diabetes
        st.session_state.patient_data["pre_diabetes"] = pre_diabetes
        st.session_state.patient_data["pre_conditions"] = pre_conditions
        st.session_state.patient_data["medications"] = medications
        st.session_state.patient_data["diet"] = diet
        st.session_state.patient_data["physical_activity"] = physical_activity
        st.session_state.patient_data["sleep_hours"] = sleep_hours
        st.session_state.patient_data["stress"] = stress
        st.session_state.patient_data["smoking_alcohol"] = smoking_alcohol
        st.session_state.patient_data["waist_circumference"] = waist_circumference


        # Full-Width Risk Assessment Section
    st.subheader("Risk Assessment Results")
    if prediction_button:
        if len(st.session_state.patient_data) < 21:
            missing_fields = 21 - len(st.session_state.patient_data)
            st.error(f"Please fill in all required fields before prediction. {missing_fields} fields remaining.")
        else:
            with st.container():
                st.markdown("---")
                with st.spinner('Calculating risk score...'):
                    input_values = {
                        'Age': st.session_state.patient_data.get('age'),
                        'Sex': st.session_state.patient_data.get('sex'),
                        'FBS': st.session_state.patient_data.get('fbs'),
                        'PPG': st.session_state.patient_data.get('ppg'),
                        'HbA1c': st.session_state.patient_data.get('hbA1c'),
                        'BloodGlycose': st.session_state.patient_data.get('blood_glycose'),
                        'Systolic': st.session_state.patient_data.get('systolic'),
                        'Diastolic': st.session_state.patient_data.get('diastolic'),
                        'Cholesterol': st.session_state.patient_data.get('chol'),
                        'SpO2Levels': st.session_state.patient_data.get('spO2'),
                        'FamilyHistory': st.session_state.patient_data.get('family_history'),
                        'GestationalDiabetes': st.session_state.patient_data.get('gest_diabetes'),
                        'PreDiabetes': st.session_state.patient_data.get('pre_diabetes'),
                        'PreExistingConditions': st.session_state.patient_data.get('pre_conditions'),
                        'Diet': st.session_state.patient_data.get('diet'),
                        'PhysicalActivity': st.session_state.patient_data.get('physical_activity'),
                        'SleepHours': st.session_state.patient_data.get('sleep_hours'),
                        'StressLevels': st.session_state.patient_data.get('stress'),
                        'SmokingAlcohol': st.session_state.patient_data.get('smoking_alcohol'),
                        'Medications': st.session_state.patient_data.get('medications'),
                        'WaistCircumferenceCM': st.session_state.patient_data.get('waist_circumference')
                    }
                        # Calculate total risk score and risk level
                missing_fields = [key for key, value in input_values.items() if value is None]
                if missing_fields:
                    st.warning(f"⚠️ Please fill in all required fields: {', '.join(missing_fields)}")

                else:
                    st.session_state.total_risk_score = calculate_risk_score(input_values)
                    st.session_state.risk_level, st.session_state.risk_description = get_risk_level(st.session_state.total_risk_score)
                    
                if "total_risk_score" in st.session_state and "risk_level" in st.session_state:
                    st.metric("Total Risk Score", f"{st.session_state.total_risk_score} points")
                    st.metric("Risk Level", st.session_state.risk_level)
                    st.info(st.session_state.risk_description)

    st.title("🧠 Dr Deuces")
    st.caption("🚀 Your AI Assistant On Healthcare Issues")
    
    for chat in st.session_state.chat_history:
        role = "You" if chat["role"] == "user" else "Bot"
        st.write(f"**{role}:** {chat['content']}")

    max_prompts = 5
    user_prompts_count = sum(1 for msg in st.session_state.chat_history if msg["role"] == "user")

    user_query = None

    if user_prompts_count >= max_prompts:
        with st.chat_message("assistant"):
            st.warning("**You have reached the maximum number of interactions.** You cannot ask more questions.")
    else:
        user_query = st.text_input("Ask a question...")

    if user_query and user_query.strip():
        user_query = user_query.strip()
        st.session_state.chat_history.append({"role": "user", "content": user_query.strip()})

        with st.chat_message("assistant"):
            response_placeholder = st.empty()
            response_placeholder.markdown("Generating response...")

        # if "total_risk_score" in st.session_state and "risk_level" in st.session_state:
        total_risk_score = st.session_state.total_risk_score
        risk_level = st.session_state.risk_level
        # else:
        response = generate_response(
            st.session_state.prediction_context, user_query,
            st.session_state.patient_data, st.session_state.prediction_result,
            total_risk_score, risk_level
            )
        
        if st.session_state.first_question and total_risk_score is not None and risk_level is not None:
            response = f"Your total risk score is {total_risk_score}, placing you in the {risk_level} risk category. {response}"
            st.session_state.first_question = False 
            
        response_placeholder.markdown(response)
        st.session_state.chat_history.append({"role": "assistant", "content": response})
        st.session_state.chat_history = st.session_state.chat_history[-10:]
        st.stop()

def generate_response(context, user_query, patient_data=None, prediction_result=None, total_risk_score=None, risk_level=None, response_limit=700):
    try:
        personalized_context = context
        if patient_data:
            patient_details = ". ".join([f"{key}: {value}" for key, value in patient_data.items()])
            personalized_context += f"\nPatient Details: {patient_details}"

        if prediction_result:
            personalized_context += f"\nPrediction Result: {prediction_result}"

        if total_risk_score is not None and risk_level is not None:
            personalized_context += f"\nTotal Risk Score: {total_risk_score} points. Risk Level: {risk_level}."

        response = llm.generate(
            prompts=[
                f"Using the following context, provide a concise and personalized health recommendation for managing or preventing diabetes. "
                f"Consider the risk level ({risk_level}) and total risk score ({total_risk_score}) in your response. "
                f"Avoid lists or bullet points, and write a cohesive response within {response_limit} characters:\n\n"
                f"{personalized_context}\n\n"
                f"Question: {user_query}\nAnswer (cohesive and concise):"
            ]
        )

        chatbot_response = response.generations[0][0].text
        chatbot_response = chatbot_response.replace("the patient", "you").replace("their", "your")

        # if total_risk_score is not None and risk_level is not None:
            # chatbot_response = f"Your total risk score is {total_risk_score}, placing you in the {risk_level} risk category. " + chatbot_response

        if len(chatbot_response) > response_limit:
            truncated_response = chatbot_response[:response_limit]
            chatbot_response = truncated_response.rsplit(".", 1)[0] + "." if "." in truncated_response else truncated_response.rsplit(" ", 1)[0] + "..."

        return chatbot_response.strip()
    except Exception as e:
        return f"Error from chatbot: {str(e)}"
        
if __name__ == '__main__':
    main()

