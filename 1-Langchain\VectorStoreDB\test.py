import os
import pickle
import ollama
import faiss
import streamlit as st
from langchain_ollama import ChatOllama
from langchain_core.output_parsers import StrOutputParser
from langchain.chains import RetrievalQA
from langchain.llms import Ollama
from langchain.prompts import PromptTemplate
from langchain.vectorstores import FAISS
from langchain.embeddings.ollama import OllamaEmbeddings
from langchain.docstore import InMemoryDocstore

from langchain_core.prompts import (
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate,
    AIMessagePromptTemplate,
    ChatPromptTemplate
)
# Custom CSS styling
st.markdown("""
<style>
    /* Existing styles */
    .main {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    .sidebar .sidebar-content {
        background-color: #2d2d2d;
    }
    .stTextInput textarea {
        color: #ffffff !important;
    }
    
    /* Add these new styles for select box */
    .stSelectbox div[data-baseweb="select"] {
        color: white !important;
        background-color: #3d3d3d !important;
    }
    
    .stSelectbox svg {
        fill: white !important;
    }
    
    .stSelectbox option {
        background-color: #2d2d2d !important;
        color: white !important;
    }
    
    /* For dropdown menu items */
    div[role="listbox"] div {
        background-color: #2d2d2d !important;
        color: white !important;
    }
</style>
""", unsafe_allow_html=True)
st.title("🧠 Dr Deuces")
st.caption("🚀 Your AI Assistant On Healthcare Issues")

# Sidebar configuration
with st.sidebar:
    st.header("⚙️ Configuration")
    selected_model = st.selectbox(
        "Choose Model",
        ["deepseek-r1:1.5b", "qwen2.5:1.5b"],
        index=0
    )
    embedding_model = OllamaEmbeddings(model=selected_model)

# initiate the chat engine

llm_engine=ChatOllama(
    model=selected_model,
    base_url="http://localhost:11434",

    temperature=0.3

)

# Initialize paths
load_dir = r"C:\Users\<USER>\OneDrive\Desktop\Langchain\1-Langchain\VectorStoreDB\qwen2.5-1.5b"

# Load FAISS index
index_path = os.path.join(load_dir, "index.faiss")
metadata_path = os.path.join(load_dir, "index.pkl")

if not os.path.exists(index_path) or not os.path.exists(metadata_path):
    st.error("🔴 FAISS index or metadata file not found. Please check the directory.")
else:
    # Load FAISS index
    index = faiss.read_index(index_path)

    # Load stored data
    with open(metadata_path, "rb") as f:
        saved_data = pickle.load(f)

    # Extract necessary components
    docstore_data = saved_data.get("docstore", {})
    index_to_docstore_id = saved_data.get("index_to_docstore_id", {})

    # Ensure valid docstore
    docstore = InMemoryDocstore(docstore_data if isinstance(docstore_data, dict) else {})

    # Fix missing IDs if needed
    for i in range(index.ntotal):
        if i not in index_to_docstore_id:
            index_to_docstore_id[i] = str(i)

    # Reconstruct FAISS vector store
    vector_store = FAISS(
        index=index, 
        docstore=docstore, 
        index_to_docstore_id=index_to_docstore_id, 
        embedding_function=embedding_model
    )

    # Initialize retriever
    retriever = vector_store.as_retriever()



# System prompt configuration
system_prompt = SystemMessagePromptTemplate.from_template(
    "You are an expert AI medical assistant. Provide concise, correct recommendations"
    "with strategic plans for healthcare issues. Always respond in English."
)

# Session state management
if "message_log" not in st.session_state:
    st.session_state.message_log = [{"role": "ai", "content": "Hi! I'm Dr Deuces. How can I help you today?"}]

# Chat container
chat_container = st.container()

# Display chat messages
with chat_container:
    for message in st.session_state.message_log:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

# Chat input and processing
user_query = st.chat_input("Ask health related question here...")

def generate_ai_response(prompt_chain):
    processing_pipeline=prompt_chain | llm_engine | StrOutputParser()
    return processing_pipeline.invoke({})

def build_prompt_chain():
    prompt_sequence = [system_prompt]
    for msg in st.session_state.message_log:
        if msg["role"] == "user":
            prompt_sequence.append(HumanMessagePromptTemplate.from_template(msg["content"]))
        elif msg["role"] == "ai":
            prompt_sequence.append(AIMessagePromptTemplate.from_template(msg["content"]))
    return ChatPromptTemplate.from_messages(prompt_sequence)

if user_query:
    # Add user message to log
    user_query = f"{st.session_state.get('prediction_results', '')} {user_query}"  # Auto-add health results
    st.session_state.message_log.append({"role": "user", "content": user_query})
    
    # Generate AI response
    with st.spinner("🧠 Processing..."):
        prompt_chain = build_prompt_chain()
        ai_response = generate_ai_response(prompt_chain)
    
    # Add AI response to log
    st.session_state.message_log.append({"role": "ai", "content": ai_response})
    
    # Rerun to update chat display
    st.rerun()