{"cells": [{"cell_type": "code", "execution_count": 7, "id": "275abe67-d694-4205-ae2c-bdac3054b2dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Qwen vector store created and saved.\n"]}], "source": ["from langchain.vectorstores import FAISS\n", "from langchain_ollama import OllamaEmbeddings\n", "from langchain.document_loaders import TextLoader\n", "\n", "# Load cleaned text file\n", "loader = TextLoader(\"./cleaned_text.txt\", encoding=\"utf-8\")\n", "documents = loader.load()\n", "\n", "# Initialize Qwen embedding\n", "embedding = OllamaEmbeddings(model=\"qwen2.5:1.5b\")\n", "\n", "# Create and save vector store\n", "vectordb = FAISS.from_documents(documents, embedding)\n", "vectordb.save_local(\"./embeddings/qwen\")\n", "\n", "print(\"✅ Qwen vector store created and saved.\")\n"]}, {"cell_type": "code", "execution_count": 10, "id": "3504c86c-6365-403a-8800-66c6f2288314", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ DeepSeek vector store created and saved.\n"]}], "source": ["from langchain.vectorstores import FAISS\n", "from langchain_ollama import OllamaEmbeddings\n", "from langchain.document_loaders import TextLoader\n", "\n", "# Load same cleaned text file\n", "loader = TextLoader(\"./cleaned_text.txt\", encoding=\"utf-8\")\n", "documents = loader.load()\n", "\n", "# Initialize DeepSeek embedding\n", "embedding = OllamaEmbeddings(model=\"deepseek-r1:1.5b\")\n", "\n", "# Create and save vector store\n", "vectordb = FAISS.from_documents(documents, embedding)\n", "vectordb.save_local(\"./embeddings/deepseek\")\n", "\n", "print(\"✅ DeepSeek vector store created and saved.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "1dec28f0-a533-437d-8331-c7b1e37f17e2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}