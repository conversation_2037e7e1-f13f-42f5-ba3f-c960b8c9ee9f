{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7ef25d12-8bb0-4859-81c0-d684eb849c9d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from langchain.docstore.document import Document\n", "\n", "def csv_to_documents(filepath):\n", "    df = pd.read_csv(filepath)\n", "    docs = []\n", "\n", "    for _, row in df.iterrows():\n", "        # Create a text blob from each row (excluding NaNs)\n", "        content = \"\\n\".join([f\"{col}: {row[col]}\" for col in df.columns if pd.notna(row[col])])\n", "        docs.append(Document(page_content=content))\n", "    \n", "    return docs\n"]}, {"cell_type": "code", "execution_count": 2, "id": "4089d84a-d833-4bc1-be14-d506d7416304", "metadata": {}, "outputs": [], "source": ["from langchain.document_loaders import TextLoader\n", "\n", "# 1. Load cleaned_text.txt\n", "loader = TextLoader(\"./cleaned_text.txt\", encoding=\"utf-8\")\n", "original_docs = loader.load()\n", "\n", "# Load CSVs and convert to documents\n", "docs_symptoms = csv_to_documents(\"./Text_FIles/symptom_checker_docs/disease_symptom_list.csv\")\n", "docs_medqa = csv_to_documents(\"./Text_files/symptom_checker_docs/medical_qa.csv\")\n", "docs_openfda = csv_to_documents(\"./Text_files/symptom_checker_docs/openfda_drugs.csv\")\n", "\n", "# 3. Combine all documents\n", "all_documents = original_docs + docs_symptoms + docs_medqa + docs_openfda\n"]}, {"cell_type": "code", "execution_count": null, "id": "2bbb3353-dbd4-4846-b850-16c060c07ae9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ Error importing OllamaEmbeddings: cannot import name 'LangSmithParams' from 'langchain_core.language_models.chat_models' (c:\\Users\\<USER>\\venv\\lib\\site-packages\\langchain_core\\language_models\\chat_models.py)\n", "Trying alternative import...\n", "✅ Successfully imported OllamaEmbeddings from alternative location\n", "✅ Embedding model initialized successfully\n"]}], "source": ["# Import with error handling\n", "try:\n", "    from langchain_community.vectorstores import FAISS\n", "except ImportError:\n", "    from langchain.vectorstores import FAISS\n", "\n", "try:\n", "    from langchain_ollama import OllamaEmbeddings\n", "    print(\"✅ Successfully imported OllamaEmbeddings\")\n", "except ImportError as e:\n", "    print(f\"❌ Error importing OllamaEmbeddings: {e}\")\n", "    print(\"Trying alternative import...\")\n", "    try:\n", "        from langchain.embeddings import OllamaEmbeddings\n", "        print(\"✅ Successfully imported OllamaEmbeddings from alternative location\")\n", "    except ImportError:\n", "        print(\"❌ Could not import OllamaEmbeddings from any location\")\n", "        raise\n", "\n", "# Create embedding with error handling\n", "try:\n", "    embedding = OllamaEmbeddings(model=\"qwen2.5:1.5b\")\n", "    print(\"✅ Embedding model initialized successfully\")\n", "except Exception as e:\n", "    print(f\"❌ Error initializing embedding model: {e}\")\n", "    raise\n", "\n", "# Create vector store\n", "try:\n", "    vectordb = FAISS.from_documents(all_documents, embedding)\n", "    vectordb.save_local(\"./Vector_Store/embeddings/qwen\")\n", "    print(\"✅ New vector store created and saved with combined data.\")\n", "except Exception as e:\n", "    print(f\"❌ Error creating vector store: {e}\")\n", "    raise\n"]}, {"cell_type": "code", "execution_count": null, "id": "4617e4c2-0169-41e1-8d1c-c97615f2a590", "metadata": {}, "outputs": [], "source": ["# Create DeepSeek vector store with error handling\n", "try:\n", "    # Use the same imports as above\n", "    embedding_deepseek = OllamaEmbeddings(model=\"deepseek-r1:1.5b\")\n", "    print(\"✅ DeepSeek embedding model initialized successfully\")\n", "    \n", "    vectordb_deepseek = FAISS.from_documents(all_documents, embedding_deepseek)\n", "    vectordb_deepseek.save_local(\"./Vector_Store/embeddings/deepseek\")\n", "    print(\"✅ DeepSeek vector store created and saved.\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error creating DeepSeek vector store: {e}\")\n", "    print(\"Make sure the deepseek-r1:1.5b model is available in Ollama\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d893660b-1ba2-4a35-b4f6-8119a5407ca0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}