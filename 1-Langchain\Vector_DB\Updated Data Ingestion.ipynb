import pandas as pd
from langchain.docstore.document import Document

def csv_to_documents(filepath):
    df = pd.read_csv(filepath)
    docs = []

    for _, row in df.iterrows():
        # Create a text blob from each row (excluding NaNs)
        content = "\n".join([f"{col}: {row[col]}" for col in df.columns if pd.notna(row[col])])
        docs.append(Document(page_content=content))
    
    return docs


from langchain.document_loaders import TextLoader

# 1. Load cleaned_text.txt
loader = TextLoader("./cleaned_text.txt", encoding="utf-8")
original_docs = loader.load()

# Load CSVs and convert to documents
docs_symptoms = csv_to_documents("./Text_files/symptom_checker_docs/data/disease_symptom_list.csv")
docs_medqa = csv_to_documents("./Text_files/symptom_checker_docs/data/medical_qa.csv")
docs_openfda = csv_to_documents("./Text_files/symptom_checker_docs/data/openfda_drugs.csv")

# 3. Combine all documents
all_documents = original_docs + docs_symptoms + docs_medqa + docs_openfda


from langchain.vectorstores import FAISS
from langchain_ollama import OllamaEmbeddings

embedding = OllamaEmbeddings(model="qwen2.5:1.5b")

vectordb = FAISS.from_documents(all_documents, embedding)
vectordb.save_local(".Vector_Store/embeddings/qwen")

print("✅ New vector store created and saved with combined data.")


from langchain.vectorstores import FAISS
from langchain_ollama import OllamaEmbeddings

embedding = OllamaEmbeddings(model="deepseek-r1:1.5b")

vectordb = FAISS.from_documents(all_documents, embedding)
vectordb.save_local(".Vector_Store/embeddings/deepseek")

print("✅ DeepSeek vector store created and saved.")


