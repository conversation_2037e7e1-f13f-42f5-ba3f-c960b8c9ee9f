# app/chatbot.py

import os
import faiss
import pickle
import numpy as np
import requests
import json
from sentence_transformers import SentenceTransformer
from langchain_ollama import ChatOllama
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate, MessagesPlaceholder
from langchain_core.runnables import RunnablePassthrough
from langchain_community.chat_message_histories import ChatMessageHistory

from app.intent_router import detect_intent
from app.module_router import map_symptoms_to_modules, SYMPTOM_MODULE_MAP
from app.history_tracker import log_user_symptoms
from app.symptom_extractor import extract_symptoms
from app.triage import check_red_flags
from app.smart_questioning import get_followup_questions
from app.feedback_logger import FeedbackLogger

OLLAMA_URL = "http://localhost:11434/api/chat"

class SymptomCheckerBot:
    def __init__(self):
        # Initialize embedding model and FAISS indexes
        self.model = SentenceTransformer('all-MiniLM-L6-v2')
        self.indexes = {
            "symptom": faiss.read_index("models/symptom_index.faiss"),
            "drug": faiss.read_index("models/drug_index.faiss"),
            "qa": faiss.read_index("models/qa_index.faiss")
        }
        self.texts = {
            "symptom": pickle.load(open("models/symptom_texts.pkl", "rb")),
            "drug": pickle.load(open("models/drug_texts.pkl", "rb")),
            "qa": pickle.load(open("models/qa_texts.pkl", "rb"))
        }
        
        # Initialize LangChain components
        self.llm = ChatOllama(model="qwen:7b-chat", temperature=0.7)
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a helpful medical assistant. Always respond in English.
Relevant medical context:
{context}"""),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
        ])
        
        # Conversation memory stores
        self.store = {}
        self.last_module_suggested = {}
        self.feedback_logger = FeedbackLogger()  

    def get_session_history(self, user_id: str) -> ChatMessageHistory:
        """Get or create chat history for a user"""
        if user_id not in self.store:
            self.store[user_id] = ChatMessageHistory()
        return self.store[user_id]

    def _get_conversation_context(self, user_id: str, max_messages: int = 3) -> str:
        """Generate conversation context from recent messages"""
        if user_id not in self.store:
            return "No history available"
        
        history = self.store[user_id].messages
        recent_messages = history[-max_messages*2:]  # Get last N exchanges (user+bot pairs)
        
        context = []
        for msg in recent_messages:
            role = "User" if msg.type == "human" else "Bot"
            context.append(f"{role}: {msg.content}")
        
        return "\n".join(context)

    def get_response(self, user_input, return_symptoms=False, user_id="test_user_001"):
        # 0️⃣ Handle feedback responses
        lowered = user_input.strip().lower()
        if lowered in {"yes", "yes.", "no", "no.", "y", "n"} and user_id in self.last_module_suggested:
            was_helpful = lowered.startswith("y")
            conversation_context = self._get_conversation_context(user_id)
            
            self.feedback_logger.log_feedback(
                user_input=user_input,
                module=self.last_module_suggested[user_id],
                was_helpful=was_helpful,
                user_id=user_id,
                conversation_context=conversation_context
            )
            
            del self.last_module_suggested[user_id]  # Clear after logging
            return "Thank you for your feedback! This helps me improve."

        # 1️⃣ Triage for urgent symptoms
        red_flags = check_red_flags(user_input)
        if red_flags:
            return (
                f"🚨 Urgent symptoms detected: {', '.join(red_flags)}.\n"
                "Please seek emergency care immediately or call your local emergency number."
            )

        # 2️⃣ Detect intent and retrieve context
        intent, _ = detect_intent(user_input)
        embedding = self.model.encode([user_input])[0]
        D, I = self.indexes[intent].search(np.array([embedding]), k=3)  # Get top 3 matches
        retrieved_texts = [self.texts[intent][i] for i in I[0]]
        retrieved_context = "\n".join(retrieved_texts)

        # 3️⃣ Initialize conversation history
        history = self.get_session_history(user_id)
        history.add_user_message(user_input)

        # 4️⃣ Generate response using LangChain
        chain = (
            RunnablePassthrough.assign(
                context=lambda _: retrieved_context,
                chat_history=lambda _: history.messages[:-1]
            )
            | self.prompt
            | self.llm
        )

        assistant_reply = chain.invoke({"input": user_input}).content
        history.add_ai_message(assistant_reply)

        # 5️⃣ Handle symptom-specific features
        module_suggestions = ""
        followup_questions = ""
        extracted_symptoms = []
        
        if intent == "symptom":
            # Extract symptoms using both methods for robustness
            extracted_symptoms = self.extract_symptoms(retrieved_texts[0]) or extract_symptoms(user_input)
            
            if extracted_symptoms:
                log_user_symptoms(user_id, extracted_symptoms)
                modules = map_symptoms_to_modules(extracted_symptoms)
                
                if modules:
                    main_module = modules[0]
                    module_suggestions = (
                        f"\n\n🔍 Based on your symptoms, I recommend starting with: "
                        f"**{main_module.replace('_', ' ').title()}**\n"
                        f"Other relevant modules: {', '.join(modules[1:]) if len(modules) > 1 else 'None'}"
                    )
                    self.last_module_suggested[user_id] = main_module
                    
                    # Generate follow-up questions
                    followups = get_followup_questions(extracted_symptoms)
                    if followups:
                        followup_questions = (
                            "\n\n❓ Additional questions to help assess your condition:\n" +
                            "\n".join(f"- {q}" for q in followups)
                        )

        # 6️⃣ Compose final response
        feedback_prompt = "\n\nWas this suggestion helpful? [Yes/No]" if module_suggestions else ""
        final_response = f"{assistant_reply}{module_suggestions}{followup_questions}{feedback_prompt}"

        if return_symptoms:
            return final_response, extracted_symptoms
        return final_response

    def extract_symptoms(self, text):
        """Legacy method to extract symptoms from retrieved text"""
        if "Symptoms:" in text and "→" in text:
            symptoms_part = text.split("→")[0]
            symptoms = symptoms_part.replace("Symptoms:", "").strip()
            return [s.strip().lower() for s in symptoms.split(",")]
        return []

    def clear_conversation_history(self, user_id: str):
        """Reset chat history for a user"""
        if user_id in self.store:
            self.store[user_id].clear()
        if user_id in self.last_module_suggested:
            del self.last_module_suggested[user_id]

    # Legacy methods kept for backward compatibility
    def query_qwen(self, user_input, retrieved_text):
        """Fallback method if LangChain fails"""
        payload = {
            "model": "qwen:7b-chat",
            "messages": [
                {"role": "system", "content": "You are a helpful medical assistant."},
                {"role": "user", "content": f"User input: {user_input}\n\nReference info:\n{retrieved_text}\n\nBased on the reference, provide a helpful answer."}
            ]
        }
        try:
            response = requests.post(OLLAMA_URL, json=payload, stream=True)
            response.raise_for_status()

            full_text = ""
            for line in response.iter_lines():
                if not line:
                    continue
                try:
                    chunk = json.loads(line.decode("utf-8"))
                    full_text += chunk.get("message", {}).get("content", "")
                except json.JSONDecodeError:
                    continue
            return full_text.strip() or None
        except Exception:
            return None

    def format_symptom_response(self, text):
        """Legacy formatting method"""
        if "→" in text:
            symptoms_text, disease_text = text.split("→")
            symptoms = symptoms_text.replace("Symptoms:", "").strip()
            disease = disease_text.replace("Disease:", "").strip()
            return f"🦠 Possible condition: {disease}\n🧾 Reported symptoms: {symptoms}"
        return f"🦠 Possible condition: {text.strip()}"
    
    def format_drug_response(self, text):
        try:
            drug_name = ""
            usage_info = ""
            if "Drug:" in text:
                drug_name = text.split("Drug:")[1].split(".")[0].strip()
            if "Information:" in text:
                info_section = text.split("Information:")[1]
                if "Used to" in info_section:
                    usage_info = "Used to " + info_section.split("Used to")[1].split("Dyspepsia")[0].strip()
                else:
                    usage_info = info_section.strip()
            usage_info = ". ".join(usage_info.split(".")[:2])
            return f"💊 Drug: {drug_name}\n📋 Common uses: {usage_info}"
        except:
            return f"💊 Drug info: {text.strip()}"

    def format_qa_response(self, text):
        return f"🤖 Here's what I found: {text.strip()}"
