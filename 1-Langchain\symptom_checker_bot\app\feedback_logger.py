import json
from datetime import datetime
from pathlib import Path
from typing import Optional

class FeedbackLogger:
    def __init__(self, log_dir: str = "feedback_logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
    def log_feedback(
        self,
        user_input: str,
        module: str,
        was_helpful: bool,
        user_id: Optional[str] = None,
        conversation_context: Optional[str] = None
    ) -> None:
        """Enhanced feedback logging with user context tracking"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id or "anonymous",
            "input": user_input,
            "suggested_module": module,
            "helpful": was_helpful,
            "context": conversation_context,
            "metadata": {
                "log_version": "2.0",
                "system": "symptom_checker_bot"
            }
        }
        
        # Daily rotating log files
        log_file = self.log_dir / f"feedback_{datetime.now().date()}.jsonl"
        
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
            
    def get_feedback_stats(self, module: Optional[str] = None) -> dict:
        """Calculate feedback statistics for analytics"""
        feedback_files = sorted(self.log_dir.glob("feedback_*.jsonl"))
        stats = {
            "total": 0,
            "helpful": 0,
            "by_module": {}
        }
        
        for file in feedback_files[-7:]:  # Last 7 days
            with open(file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line)
                        stats["total"] += 1
                        if entry["helpful"]:
                            stats["helpful"] += 1
                            
                        mod = entry["suggested_module"]
                        stats["by_module"].setdefault(mod, {
                            "total": 0,
                            "helpful": 0,
                            "helpfulness_rate": 0.0
                        })
                        stats["by_module"][mod]["total"] += 1
                        if entry["helpful"]:
                            stats["by_module"][mod]["helpful"] += 1
                            
                    except json.JSONDecodeError:
                        continue
        
        # Calculate rates
        if stats["total"] > 0:
            stats["helpfulness_rate"] = stats["helpful"] / stats["total"]
            
        for mod in stats["by_module"]:
            mod_stats = stats["by_module"][mod]
            if mod_stats["total"] > 0:
                mod_stats["helpfulness_rate"] = mod_stats["helpful"] / mod_stats["total"]
                
        if module:
            return {
                "module": module,
                **stats["by_module"].get(module, {"total": 0, "helpful": 0})
            }
        return stats
# Add this at the bottom of feedback_logger.py
_logger = FeedbackLogger()

def log_feedback(*args, **kwargs):
    """Public interface for feedback logging"""
    return _logger.log_feedback(*args, **kwargs)