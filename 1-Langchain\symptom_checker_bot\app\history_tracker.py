# app/symptom_tracker.py

import json
from datetime import datetime
from typing import List

SYMPTOM_LOG_PATH = "data/user_symptom_log.json"

def log_user_symptoms(user_id: str, symptoms: List[str]):
    try:
        with open(SYMPTOM_LOG_PATH, "r") as f:
            logs = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        logs = {}

    logs.setdefault(user_id, []).append({
        "timestamp": datetime.utcnow().isoformat(),
        "symptoms": symptoms
    })

    with open(SYMPTOM_LOG_PATH, "w") as f:
        json.dump(logs, f, indent=2)

def get_user_symptom_history(user_id: str) -> List[dict]:
    try:
        with open(SYMPTOM_LOG_PATH, "r") as f:
            logs = json.load(f)
            return logs.get(user_id, [])
    except (FileNotFound<PERSON>rror, json.JSONDecodeError):
        return []
