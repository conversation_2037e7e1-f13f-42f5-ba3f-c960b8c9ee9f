# app/intent_router.py

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.svm import LinearSVC
from sklearn.pipeline import Pipeline
import joblib
import os

class IntentClassifier:
    def __init__(self):
        self.model = Pipeline([
            ('tfidf', TfidfVectorizer()),
            ('clf', LinearSVC())
        ])
        self.labels = ["symptom_report", "symptom_ask", "drug", "qa"]
        self.model_path = "app/intent_classifier.joblib"

        if os.path.exists(self.model_path):
            self.model = joblib.load(self.model_path)
        else:
            self.train_default_model()

    def train_default_model(self):
        training_data = [
            # Symptom reports (user describing how they feel)
            ("I have a headache and fever", "symptom_report"),
            ("My stomach hurts", "symptom_report"),
            ("I feel dizzy and tired", "symptom_report"),
            ("Experiencing chest pain and shortness of breath", "symptom_report"),
            ("I feel weak", "symptom_report"),

            # Symptom asks (asking about disease symptoms)
            ("What are the symptoms of flu", "symptom_ask"),
            ("Symptoms of malaria", "symptom_ask"),
            ("Show me the symptoms of tuberculosis", "symptom_ask"),
            ("Tell me the symptoms of hypertension", "symptom_ask"),

            # Drug questions
            ("What is paracetamol", "drug"),
            ("Side effects of ibuprofen", "drug"),
            ("What is the dosage of aspirin", "drug"),
            ("Medicine for headache", "drug"),
            ("What are the indications of codeine", "drug"),

            # General Q&A
            ("How to stay healthy", "qa"),
            ("Who is at risk for Lymphocytic Choriomeningitis (LCM)?", "qa"),
            ("What are the treatments for Parasites - Cysticercosis?", "qa"),
            ("Explain photosynthesis", "qa"),
            ("How can I manage hypertension?", "qa"),
            ("What is the best treatment for asthma?", "qa"),
        ]

        texts = [text for text, label in training_data]
        labels = [label for text, label in training_data]

        self.model.fit(texts, labels)
        joblib.dump(self.model, self.model_path)

    def predict(self, text):
        return self.model.predict([text])[0]

# Initialize
intent_classifier = IntentClassifier()

def detect_intent(user_input):
    full_intent = intent_classifier.predict(user_input)
    # Group symptom intents together for indexing, return full label for logic
    base_intent = "symptom" if "symptom" in full_intent else full_intent
    return base_intent, full_intent  # ("symptom", "symptom_ask")
