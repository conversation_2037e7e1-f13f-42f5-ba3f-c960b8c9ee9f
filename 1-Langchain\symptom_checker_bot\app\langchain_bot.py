# app/langchain_symptom_bot.py 🚑🤖
"""
Intent‑aware symptom checker:
• Chat<PERSON>llama (qwen:7b-chat)
• FAISS indexes: symptom, drug, general QA
• Intent routing via app.intent_router.detect_intent
• Symptom-based module suggestions & history logging
• Triage logic, smart follow‑up questions, feedback loop  ◀ NEW
• Per-user conversational memory with <PERSON><PERSON><PERSON><PERSON>
"""

# ───────────────────────── Imports ─────────────────────────
from langchain_ollama import ChatOllama
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables import RunnablePassthrough
from langchain_community.vectorstores import FAISS
from langchain.memory import ChatMessageHistory
from langchain_huggingface import HuggingFaceEmbeddings
#________________Local imports_______________________________________
from app.module_router import map_inputs_to_modules, SYMPTOM_MODULE_MAP
from app.history_tracker import log_user_symptoms
from app.intent_router import detect_intent
from app.symptom_extractor import extract_symptoms        
from app.triage import check_red_flags                    
from app.smart_questioning import get_followup_questions  
from app.feedback_logger import log_feedback              

# ───────────────────────── LLM & Prompt ────────────────────
llm = ChatOllama(model="qwen:7b-chat", temperature=0.7)

prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a helpful medical assistant, answer the user's question clearly and simply. If you don’t have specific context, still try to help with general advice and always respond in English.
Relevant medical context:
{context}"""),
    MessagesPlaceholder(variable_name="chat_history"),
    ("human", "{input}"),
])

# ─────────────────────── Memory Store ──────────────────────
# Store both chat history and last suggested module per user
store = {}
last_module_suggested = {}

def get_session_history(user_id: str) -> ChatMessageHistory:
    if user_id not in store:
        store[user_id] = ChatMessageHistory()
    return store[user_id]

# ─────────────────────── Embeddings & Stores ───────────────
embedding_model = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")

def load_store(index_name: str):
    return FAISS.load_local(
        folder_path="models",
        embeddings=embedding_model,
        index_name=index_name,
        allow_dangerous_deserialization=True,
    ).as_retriever()

symptom_retriever = load_store("symptom_index")
drug_retriever    = load_store("drug_index")
general_retriever = load_store("qa_index")

# ─────────────────────── Interaction API ───────────────────
def handle_user_input(user_input: str, user_id: str = "test_user_001") -> str:
    """
    Routes query by intent → retrieves context → generates answer.
    Adds triage check, module suggestions, follow‑up questions, and feedback.
    """
    # 0️⃣ Check if the user is replying "Yes/No" to give feedback
    lowered = user_input.strip().lower()
    if lowered in {"yes", "yes.", "no", "no."} and user_id in last_module_suggested:
        was_helpful = lowered.startswith("y")
        log_feedback(
            user_input="feedback",                   # we log the feedback itself
            module=last_module_suggested[user_id],
            was_helpful=was_helpful,
        )
        return "Thanks for letting me know! Your feedback helps me improve."

    # 1️⃣ Triage logic – urgent care first
    red_flags = check_red_flags(user_input)
    if red_flags:
        return (
            f"⚠️ Critical symptom detected: **{', '.join(red_flags)}**.\n"
            "Please seek emergency medical attention immediately "
            "or call your local emergency number."
        )

    # 2️⃣ Detect intent
    primary_intent, _ = detect_intent(user_input)

    # 3️⃣ Select retriever
    retriever = {
        "symptom": symptom_retriever,
        "drug":    drug_retriever
    }.get(primary_intent, general_retriever)

    # 4️⃣ Retrieve context
    retrieved_docs    = retriever.invoke(user_input)
    retrieved_context = "\n".join(doc.page_content for doc in retrieved_docs)
    if not retrieved_context.strip():
        retrieved_context = (
            "No specific medical documents matched your query. "
            "Answer as best as you can from general medical knowledge."
        )


    # 5️⃣ Symptom‑specific processing
    module_suggestions = ""
    followup_text      = ""
    if primary_intent == "symptom":
        symptoms = extract_symptoms(user_input)
        if symptoms:
            log_user_symptoms(user_id, symptoms)
            modules = map_inputs_to_modules(symptoms)
            if modules:
                main_module      = modules[0]
                module_suggestions = (
                    "\n\n📍 I suggest starting with the "
                    f"**{main_module.replace('_', ' ').title()}** module."
                    f"\nOther helpful modules: {' / '.join(modules)}."
                    "\nShall I open one of them for you?"
                )
                # Store for feedback tracking
                last_module_suggested[user_id] = main_module
                # Smart follow‑up
                followups = get_followup_questions(symptoms)
                if followups:
                    followup_text = (
                        "\n\n🔍 A few follow‑up questions:\n" +
                        "\n".join(f"- {q}" for q in followups)
                    )
            else:
                module_suggestions = (
                    "\n\nℹ️ I'm not sure which module fits best yet—"
                    "could you describe your symptoms in more detail?"
                )

    # 6️⃣ Chat history handling
    history = get_session_history(user_id)
    history.add_user_message(user_input)

    # 7️⃣ LLM chain
    chain = (
        RunnablePassthrough.assign(
            context      = lambda _: retrieved_context,
            chat_history = lambda _: history.messages[:-1]
        )
        | prompt
        | llm
    )

    assistant_reply = chain.invoke({"input": user_input}).content
    history.add_ai_message(assistant_reply)

    # 8️⃣ Append suggestions, follow‑ups, and feedback prompt
    feedback_prompt = (
        "\n\nWas this suggestion helpful? [Yes] / [No]"
        if module_suggestions else ""
    )
    return f"{assistant_reply}{module_suggestions}{followup_text}{feedback_prompt}"
