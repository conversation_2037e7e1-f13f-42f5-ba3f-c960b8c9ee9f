# app/module_router.py

from __future__ import annotations
from typing import List, Set, Dict
import json
import re
from pathlib import Path

# ----------------------------
# 1. Module Priority
# ----------------------------
MODULE_PRIORITY = {
    "triage": 0,
    "vitals": 1,
    "bmi": 2,
    "lung": 3,
    "malaria_widal": 4,
    "hepatitis_serology": 5,
    "health_score": 6,
    "progress_tracker": 7,
    "chronic_risk": 8,
    "lipid": 9,
    "kidney": 10,
    "liver": 11,
    "cardiovascular": 12,
    "diabetes": 13,
    "stress": 14,
    "menstrual": 15,
    "auto_bio": 16,
    "appointment_scheduler": 17,
    "device_recommender": 18,
    "doc_summarizer": 19,
    "symptom_checker": 20,
    "lab_explainer": 21,
    "lifestyle_coach": 22,
    "chronic_tracker": 23,
    "followup_reminder": 24,
    "wellness_digest": 25,
}

# ----------------------------
# 2. <PERSON>ympt<PERSON> → Module Mapping
# ----------------------------
SYMPTOM_MODULE_MAP: Dict[str, Set[str]] = {
    "fatigue": {"liver", "stress", "health_score", "chronic_tracker"},
    "swollen feet": {"kidney"},
    "dark urine": {"liver", "kidney"},
    "headache": {"stress"},
    "abdominal pain": {"liver"},
    "foamy urine": {"kidney"},
    "sleep issues": {"stress"},
    "yellow skin": {"liver"},
    "itchy skin": {"liver"},
    "chest pain": {"cardiovascular"},
    "shortness of breath": {"lung", "cardiovascular"},
    "frequent urination": {"diabetes"},
    "excessive thirst": {"diabetes"},
    "irregular periods": {"menstrual"},
    "persistent cough": {"lung"},
    "loss of appetite": {"stress"},
    "positive malaria test": {"malaria_widal"},
    "positive widal test": {"malaria_widal"},
    "yellow eyes": {"hepatitis_serology"},
    "weight gain": {"bmi", "lifestyle_coach"},
    "weight loss": {"bmi", "chronic_risk"},
    "high blood pressure": {"chronic_risk", "cardiovascular"},
    "blood sugar spike": {"progress_tracker"},
    "forgetting meds": {"followup_reminder"},
    "unusual test result": {"lab_explainer"},
    "confusing lab report": {"doc_summarizer"},
    "can't schedule appointment": {"appointment_scheduler"},
    "elevated cholesterol": {"lipid"},
    "nausea": {"lab_explainer", "liver"},
    "my test result is strange": {"lab_explainer"},
}

# ----------------------------
# 3. Condition → Module Mapping
# ----------------------------
CONDITION_MODULE_MAP: Dict[str, Set[str]] = {
    "hepatitis": {"liver", "hepatitis_serology"},
    "non alcoholic fatty liver disease": {"liver"},
    "chronic kidney disease": {"kidney"},
    "hypertension": {"cardiovascular", "chronic_risk"},
    "type 2 diabetes": {"diabetes", "lifestyle_coach", "chronic_tracker"},
    "type 1 diabetes": {"chronic_tracker", "progress_tracker"},
    "asthma": {"lung"},
    "malaria": {"malaria_widal"},
    "tuberculosis": {"lung"},
    "obesity": {"bmi"},
    "high cholesterol": {"lipid"},
    "ckd": {"kidney"},
}

# ----------------------------
# 4. Symptom Synonyms
# ----------------------------
SYMPTOM_SYNONYMS = {
    r"\bpuffy (feet|ankles)\b": "swollen feet",
    r"\b(brown|tea\-coloured) urine\b": "dark urine",
    r"\b(tired|exhausted|worn out)\b": "fatigue",
    r"\b(i can.?t sleep|insomnia)\b": "sleep issues",
    r"\b(my test says malaria positive)\b": "positive malaria test",
    r"\b(bloated|swollen abdomen)\b": "abdominal swelling",
    r"\b(can.?t breathe|difficulty breathing)\b": "shortness of breath",
    r"\b(forgot my medication|missed my dose)\b": "forgetting meds",
    r"\b(confused by lab|don.?t understand test)\b": "confusing lab report",
    r"\b(schedule appointment|book doctor)\b": "can't schedule appointment",
    r"\b(weird|strange) result\b": "my test result is strange",
}

# ----------------------------
# 5. Helpers
# ----------------------------
def _load_external_map(json_path: str | Path, target: dict[str, Set[str]]) -> None:
    p = Path(json_path)
    if p.exists():
        with p.open(encoding="utf-8") as f:
            data = json.load(f)
        for k, mods in data.items():
            target.setdefault(k.lower(), set()).update(set(mods))

def _canonicalize_symptom(text: str) -> str:
    for pattern, canonical in SYMPTOM_SYNONYMS.items():
        if re.search(pattern, text, flags=re.I):
            return canonical
    return text.lower().strip()

# ----------------------------
# 6. Main Router Function
# ----------------------------
def map_inputs_to_modules(
    inputs: List[str],
    *,
    input_type: str = "auto",  # or "symptom" or "condition"
    external_symptom_map: str | Path | None = None,
    external_condition_map: str | Path | None = None,
) -> List[str]:
    if external_symptom_map:
        _load_external_map(external_symptom_map, SYMPTOM_MODULE_MAP)
    if external_condition_map:
        _load_external_map(external_condition_map, CONDITION_MODULE_MAP)

    recommended: Set[str] = set()

    for item in inputs:
        item_low = item.lower()
        typ = input_type
        if typ == "auto":
            typ = "condition" if " " in item_low and any(
                kw in item_low for kw in CONDITION_MODULE_MAP.keys()
            ) else "symptom"

        if typ == "symptom":
            canonical = _canonicalize_symptom(item_low)
            mods = SYMPTOM_MODULE_MAP.get(canonical)
        else:
            mods = None
            for cond_key in CONDITION_MODULE_MAP:
                if cond_key in item_low:
                    mods = CONDITION_MODULE_MAP[cond_key]
                    break

        if mods:
            recommended.update(mods)

    return sorted(recommended, key=lambda m: MODULE_PRIORITY.get(m, 99))

# ----------------------------
# 7. Test Example
# ----------------------------
if __name__ == "__main__":
    demo = [
        "I'm tired",
        "My feet are puffy",
        "My test says malaria positive",
        "I missed my medication",
        "My test result is strange",
        "I gained weight",
    ]
    print(map_inputs_to_modules(demo))
