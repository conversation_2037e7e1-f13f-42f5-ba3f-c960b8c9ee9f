# app/smart_questioning.py

SYMPTOM_QUESTIONS = {
    "fatigue": [
        "How long have you been feeling tired?",
        "Does rest help relieve it?",
        "Are you experiencing shortness of breath too?"
    ],
    "chest pain": [
        "When did the pain start?",
        "Is it sharp or dull?",
        "Does it spread to your arms or jaw?"
    ],
    "abdominal pain": [
        "Can you point to the exact location?",
        "Is the pain constant or comes and goes?",
        "Do you feel nauseous or have vomiting?"
    ],
    "frequent urination": [
        "Is it worse at night?",
        "Do you also feel thirsty all the time?",
        "Is your urine clear or foamy?"
    ],
}

def get_followup_questions(symptoms: list[str]) -> list[str]:
    """Returns relevant follow-up questions for given symptoms."""
    questions = []
    for sym in symptoms:
        if sym in SYMPTOM_QUESTIONS:
            questions.extend(SYMPTOM_QUESTIONS[sym])
    return questions[:3]  # max 3 questions per turn
