# app/triage.py

RED_FLAGS = {
    "chest pain",
    "difficulty breathing",
    "loss of consciousness",
    "sudden confusion",
    "severe abdominal pain",
    "bleeding that won’t stop",
    "unexplained swelling",
    "vision loss",
    "uncontrolled vomiting",
    "weakness on one side",
}

def check_red_flags(text: str) -> list[str]:
    """Returns any red flag symptoms found in user input."""
    matches = [flag for flag in RED_FLAGS if flag in text.lower()]
    return matches
