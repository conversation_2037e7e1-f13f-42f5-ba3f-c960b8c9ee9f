# train/build_indexes.py

import os
from sentence_transformers import SentenceTransformer
from langchain_community.vectorstores import FAISS
from langchain_huggingface import HuggingFaceEmbeddings
from preprocess import load_datasets, prepare_texts

def build_and_save_faiss(texts, save_dir, save_name, embedding_model):
    # Create FAISS vector store from texts
    store = FAISS.from_texts(texts, embedding_model)
    store.save_local(save_dir, save_name)

if __name__ == "__main__":
    # Use HuggingFaceEmbeddings wrapper for LangChain compatibility
    model = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")

    symptoms_df, drugs_df, qa_df = load_datasets()
    symptom_texts, drug_texts, qa_texts = prepare_texts(symptoms_df, drugs_df, qa_df)

    os.makedirs("models", exist_ok=True)

    build_and_save_faiss(symptom_texts, "models", "symptom_index", model)
    build_and_save_faiss(drug_texts, "models", "drug_index", model)
    build_and_save_faiss(qa_texts, "models", "qa_index", model)
