# train/preprocess.py

import pandas as pd

def load_datasets():
    symptoms_df = pd.read_csv("data/disease_symptom_list.csv", encoding='latin-1')
    drugs_df = pd.read_csv("data/openfda_drugs.csv", encoding='latin-1')
    qa_df = pd.read_csv("data/medical_qa.csv", encoding='latin-1')
    
    # Rename drug columns as specified
    drugs_df.rename(columns={
        'drug': 'drug_name',
        'reaction': 'side_effects',
        'indications_and_usage': 'uses',
        'dosage_and_administration': 'dosage'
    }, inplace=True)
    
    return symptoms_df, drugs_df, qa_df

def prepare_texts(symptoms_df, drugs_df, qa_df):
    # Prepare symptom texts (unchanged)
    symptom_texts = [f"Symptoms: {row['symptoms']} → Disease: {row['disease']}" 
                    for _, row in symptoms_df.iterrows()]
    
    # Create a copy of drugs_df to avoid SettingWithCopyWarning
    drugs_df_copy = drugs_df.copy()
    
    # Combine drug information into a single text field as specified
    drugs_df_copy['drug_info'] = (
        drugs_df_copy['uses'].fillna('') + " " +
        drugs_df_copy['side_effects'].fillna('') + " " +
        drugs_df_copy['dosage'].fillna('')
    )
    
    # Keep only the specified columns
    drugs_df_copy = drugs_df_copy[['drug_name', 'drug_info']]
    
    # Prepare drug texts using the combined information
    drug_texts = [f"Drug: {row['drug_name']}. Information: {row['drug_info']}" 
                for _, row in drugs_df_copy.iterrows()]
    
    # Prepare QA texts (unchanged)
    qa_texts = [f"Q: {row['Question']} A: {row['Answer']}" 
               for _, row in qa_df.iterrows()]
    
    return symptom_texts, drug_texts, qa_texts