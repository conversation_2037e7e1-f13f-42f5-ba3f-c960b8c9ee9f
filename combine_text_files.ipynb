import os
import re
import textwrap

def clean_text(text, 
               remove_extra_whitespace=True,
               normalize_case=False,
               remove_special_chars=False,
               remove_numbers=False,
               min_word_length=1,
               custom_replacements=None):
    """
    Unified text cleaning function that can be customized for different use cases.
    
    Args:
        text (str): Input text to clean
        remove_extra_whitespace (bool): Remove extra spaces and normalize whitespace
        normalize_case (bool): Convert to lowercase
        remove_special_chars (bool): Remove special characters (keep only alphanumeric and spaces)
        remove_numbers (bool): Remove all numbers
        min_word_length (int): Minimum word length to keep
        custom_replacements (dict): Dictionary of custom string replacements
    
    Returns:
        str: Cleaned text
    """
    if not isinstance(text, str):
        text = str(text)
    
    # Remove leading/trailing whitespace and dedent
    text = textwrap.dedent(text).strip()
    
    # Custom replacements
    if custom_replacements:
        for old, new in custom_replacements.items():
            text = text.replace(old, new)
    
    # Normalize case
    if normalize_case:
        text = text.lower()
    
    # Remove special characters
    if remove_special_chars:
        text = re.sub(r'[^\w\s]', ' ', text)
    
    # Remove numbers
    if remove_numbers:
        text = re.sub(r'\d+', ' ', text)
    
    # Remove extra whitespace
    if remove_extra_whitespace:
        text = re.sub(r'\s+', ' ', text).strip()
    
    # Filter words by minimum length
    if min_word_length > 1:
        words = text.split()
        words = [word for word in words if len(word) >= min_word_length]
        text = ' '.join(words)
    
    return text

def load_text_files():
    """
    Load text files from both Vector_DB and VectorStoreDB directories.
    
    Returns:
        tuple: (vector_db_text, vector_store_db_text)
    """
    vector_db_path = "1-Langchain/Vector_DB/cleaned_text.txt"
    vector_store_db_path = "1-Langchain/VectorStoreDB/cleaned_text.txt"
    
    vector_db_text = ""
    vector_store_db_text = ""
    
    # Load Vector_DB text
    if os.path.exists(vector_db_path):
        with open(vector_db_path, 'r', encoding='utf-8') as f:
            vector_db_text = f.read()
        print(f"✅ Loaded Vector_DB text: {len(vector_db_text):,} characters")
    else:
        print(f"❌ Vector_DB file not found: {vector_db_path}")
    
    # Load VectorStoreDB text
    if os.path.exists(vector_store_db_path):
        with open(vector_store_db_path, 'r', encoding='utf-8') as f:
            vector_store_db_text = f.read()
        print(f"✅ Loaded VectorStoreDB text: {len(vector_store_db_text):,} characters")
    else:
        print(f"❌ VectorStoreDB file not found: {vector_store_db_path}")
    
    return vector_db_text, vector_store_db_text

# Load the texts
vector_db_text, vector_store_db_text = load_text_files()

# Clean both texts using the unified cleaning function
print("\nCleaning Vector_DB text...")
cleaned_vector_db = clean_text(
    vector_db_text,
    remove_extra_whitespace=True,
    normalize_case=False,  # Keep original case for medical terms
    remove_special_chars=False,  # Keep punctuation for readability
    remove_numbers=False,  # Keep numbers for medical data
    min_word_length=2
)

print("Cleaning VectorStoreDB text...")
cleaned_vector_store_db = clean_text(
    vector_store_db_text,
    remove_extra_whitespace=True,
    normalize_case=False,  # Keep original case
    remove_special_chars=False,  # Keep punctuation
    remove_numbers=False,  # Keep numbers for statistics
    min_word_length=2
)

print(f"\nVector_DB cleaned text length: {len(cleaned_vector_db):,} characters")
print(f"VectorStoreDB cleaned text length: {len(cleaned_vector_store_db):,} characters")

# Combine the texts with clear separation
combined_text = f"""
=== MEDICAL RESEARCH AND INFECTIOUS DISEASES ===
{cleaned_vector_db}

=== CARDIOVASCULAR EXERCISE AND PHYSICAL ACTIVITY ===
{cleaned_vector_store_db}
""".strip()

print(f"\nCombined text length: {len(combined_text):,} characters")
print(f"Combined text word count: {len(combined_text.split()):,} words")

# Save the combined cleaned text
output_path = "combined_cleaned_text.txt"
with open(output_path, 'w', encoding='utf-8') as f:
    f.write(combined_text)

print(f"\n✅ Combined cleaned text saved to: {output_path}")

# Display statistics about the combined text
print("\n=== TEXT STATISTICS ===")
print(f"Vector_DB original length: {len(vector_db_text):,} characters")
print(f"Vector_DB cleaned length: {len(cleaned_vector_db):,} characters")
print(f"VectorStoreDB original length: {len(vector_store_db_text):,} characters")
print(f"VectorStoreDB cleaned length: {len(cleaned_vector_store_db):,} characters")
print(f"Combined text length: {len(combined_text):,} characters")
print(f"Combined text word count: {len(combined_text.split()):,} words")

# Show preview of each section
print("\n=== VECTOR_DB CONTENT PREVIEW ===")
print(cleaned_vector_db[:400] + "...")

print("\n=== VECTORSTOREDB CONTENT PREVIEW ===")
print(cleaned_vector_store_db[:400] + "...")

# Create a version without section headers (just concatenated)
simple_combined = f"{cleaned_vector_db}\n\n{cleaned_vector_store_db}"

# Save simple version
simple_output_path = "simple_combined_text.txt"
with open(simple_output_path, 'w', encoding='utf-8') as f:
    f.write(simple_combined)

print(f"\n✅ Simple combined text (no headers) saved to: {simple_output_path}")

# Create a more heavily cleaned version (lowercase, no special chars)
heavily_cleaned_vector_db = clean_text(
    vector_db_text,
    remove_extra_whitespace=True,
    normalize_case=True,
    remove_special_chars=True,
    remove_numbers=False,
    min_word_length=3
)

heavily_cleaned_vector_store_db = clean_text(
    vector_store_db_text,
    remove_extra_whitespace=True,
    normalize_case=True,
    remove_special_chars=True,
    remove_numbers=False,
    min_word_length=3
)

heavily_cleaned_combined = f"{heavily_cleaned_vector_db}\n\n{heavily_cleaned_vector_store_db}"

# Save heavily cleaned version
heavy_output_path = "heavily_cleaned_combined_text.txt"
with open(heavy_output_path, 'w', encoding='utf-8') as f:
    f.write(heavily_cleaned_combined)

print(f"✅ Heavily cleaned combined text saved to: {heavy_output_path}")
print(f"   (lowercase, no special chars, min word length 3)")