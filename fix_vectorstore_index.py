#!/usr/bin/env python3
"""
Fix VectorStore Index - Resolves KeyError: np.int64(0) issue
This script fixes the index mapping issue in existing FAISS vector stores.
"""

import os
import numpy as np
import warnings
warnings.filterwarnings("ignore")

def fix_numpy_compatibility():
    """Fix numpy compatibility issues."""
    if hasattr(np, 'int'):
        np.int = int
    if hasattr(np, 'float'):
        np.float = float
    if hasattr(np, 'bool'):
        np.bool = bool
    print("✅ Fixed numpy compatibility")

def import_components():
    """Import required components with fallbacks."""
    try:
        from langchain_community.vectorstores import FAISS
        print("✅ Imported FAISS from langchain_community")
    except ImportError:
        from langchain.vectorstores import FAISS
        print("✅ Imported FAISS from langchain")
    
    try:
        from langchain_ollama import OllamaEmbeddings
        print("✅ Imported OllamaEmbeddings from langchain_ollama")
    except ImportError:
        try:
            from langchain_community.embeddings import OllamaEmbeddings
            print("✅ Imported OllamaEmbeddings from langchain_community")
        except ImportError:
            from langchain.embeddings import OllamaEmbeddings
            print("✅ Imported OllamaEmbeddings from langchain")
    
    return FAISS, OllamaEmbeddings

def fix_vectorstore_index(vectorstore):
    """
    Fix the index mapping in a FAISS vector store.
    
    Args:
        vectorstore: FAISS vector store object
    
    Returns:
        Fixed vector store
    """
    try:
        if hasattr(vectorstore, 'index_to_docstore_id'):
            print("Fixing index mapping...")
            
            # Get current mapping
            current_mapping = vectorstore.index_to_docstore_id
            print(f"Current mapping has {len(current_mapping)} entries")
            
            # Show sample of current keys and their types
            sample_keys = list(current_mapping.keys())[:3]
            print(f"Sample keys: {sample_keys}")
            print(f"Key types: {[type(k).__name__ for k in sample_keys]}")
            
            # Convert numpy int64 keys to regular int
            fixed_mapping = {}
            for key, value in current_mapping.items():
                if isinstance(key, np.integer):
                    fixed_mapping[int(key)] = value
                else:
                    fixed_mapping[key] = value
            
            # Update the mapping
            vectorstore.index_to_docstore_id = fixed_mapping
            
            print(f"✅ Fixed index mapping: {len(fixed_mapping)} entries")
            print(f"New key types: {[type(k).__name__ for k in list(fixed_mapping.keys())[:3]]}")
            
            return vectorstore
        else:
            print("⚠️ Vector store doesn't have index_to_docstore_id attribute")
            return vectorstore
            
    except Exception as e:
        print(f"❌ Error fixing index mapping: {e}")
        return vectorstore

def test_vectorstore(vectorstore, test_query="tuberculosis symptoms"):
    """
    Test the vector store with a similarity search.
    
    Args:
        vectorstore: FAISS vector store object
        test_query: Query to test with
    
    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"\n🔍 Testing with query: '{test_query}'")
        results = vectorstore.similarity_search(test_query, k=2)
        
        print(f"✅ Search successful! Found {len(results)} results:")
        for i, result in enumerate(results, 1):
            preview = result.page_content[:150].replace('\n', ' ')
            print(f"   {i}. {preview}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Search failed: {e}")
        print(f"Error type: {type(e).__name__}")
        return False

def fix_existing_vectorstore(vectorstore_path, model_name="qwen2.5:1.5b"):
    """
    Load and fix an existing vector store.
    
    Args:
        vectorstore_path: Path to the vector store
        model_name: Embedding model name
    
    Returns:
        Fixed vector store or None
    """
    try:
        FAISS, OllamaEmbeddings = import_components()
        
        if not os.path.exists(vectorstore_path):
            print(f"❌ Vector store not found at: {vectorstore_path}")
            return None
        
        print(f"Loading vector store from: {vectorstore_path}")
        
        # Create embeddings
        embeddings = OllamaEmbeddings(model=model_name)
        print(f"✅ Created embeddings with model: {model_name}")
        
        # Load vector store
        vectorstore = FAISS.load_local(
            vectorstore_path, 
            embeddings, 
            allow_dangerous_deserialization=True
        )
        print("✅ Vector store loaded")
        
        # Fix the index mapping
        fixed_vectorstore = fix_vectorstore_index(vectorstore)
        
        # Test the fixed vector store
        if test_vectorstore(fixed_vectorstore):
            print("✅ Vector store is working correctly!")
            return fixed_vectorstore
        else:
            print("❌ Vector store still has issues")
            return None
            
    except Exception as e:
        print(f"❌ Error fixing vector store: {e}")
        return None

def save_fixed_vectorstore(vectorstore, save_path):
    """
    Save the fixed vector store.
    
    Args:
        vectorstore: Fixed vector store
        save_path: Path to save to
    
    Returns:
        True if successful, False otherwise
    """
    try:
        # Create directory if needed
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # Save the vector store
        vectorstore.save_local(save_path)
        print(f"✅ Fixed vector store saved to: {save_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error saving fixed vector store: {e}")
        return False

def main():
    """Main function to fix vector store issues."""
    print("=== VectorStore Index Fixer ===\n")
    
    # Fix numpy compatibility first
    fix_numpy_compatibility()
    
    # Common vector store paths to try
    possible_paths = [
        "./vector_store",
        "./Vector_Store",
        "./embeddings",
        "./Vector_Store/embeddings",
        "./Vector_Store/embeddings/qwen",
        "./Vector_Store/embeddings/deepseek"
    ]
    
    # Try to find and fix vector stores
    for path in possible_paths:
        if os.path.exists(path):
            print(f"\n📁 Found vector store at: {path}")
            
            # Try to fix it
            fixed_vectorstore = fix_existing_vectorstore(path)
            
            if fixed_vectorstore:
                # Save the fixed version
                fixed_path = f"{path}_fixed"
                if save_fixed_vectorstore(fixed_vectorstore, fixed_path):
                    print(f"🎉 Successfully fixed and saved to: {fixed_path}")
                else:
                    print(f"⚠️ Fixed but couldn't save to: {fixed_path}")
            else:
                print(f"❌ Could not fix vector store at: {path}")
        else:
            print(f"⚠️ No vector store found at: {path}")
    
    print("\n📋 Summary:")
    print("   - This script fixes the KeyError: np.int64(0) issue")
    print("   - It converts numpy int64 keys to regular Python int")
    print("   - Fixed vector stores are saved with '_fixed' suffix")
    print("   - Use the fixed version in your applications")

if __name__ == "__main__":
    main()
