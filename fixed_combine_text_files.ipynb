{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Fixed: Combine cleaned_text.txt Files\n", "\n", "This notebook combines the cleaned_text.txt files from both Vector_DB and VectorStoreDB directories with compatibility fixes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compatibility fix for ForwardRef issue\n", "import sys\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning)\n", "\n", "# Fix for typing issues\n", "try:\n", "    from typing import ForwardRef\n", "    if hasattr(ForwardRef, '_evaluate'):\n", "        original_evaluate = ForwardRef._evaluate\n", "        def patched_evaluate(self, globalns=None, localns=None, recursive_guard=None):\n", "            if recursive_guard is None:\n", "                recursive_guard = frozenset()\n", "            return original_evaluate(self, globalns, localns, recursive_guard=recursive_guard)\n", "        ForwardRef._evaluate = patched_evaluate\n", "except Exception as e:\n", "    print(f\"Note: Could not apply ForwardRef patch: {e}\")\n", "\n", "import os\n", "import re\n", "import textwrap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple Text Cleaning Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def clean_text(text, \n", "               remove_extra_whitespace=True,\n", "               normalize_case=False,\n", "               remove_special_chars=False,\n", "               remove_numbers=False,\n", "               min_word_length=1):\n", "    \"\"\"\n", "    Simple text cleaning function.\n", "    \n", "    Args:\n", "        text (str): Input text to clean\n", "        remove_extra_whitespace (bool): Remove extra spaces and normalize whitespace\n", "        normalize_case (bool): Convert to lowercase\n", "        remove_special_chars (bool): Remove special characters\n", "        remove_numbers (bool): Remove all numbers\n", "        min_word_length (int): Minimum word length to keep\n", "    \n", "    Returns:\n", "        str: Cleaned text\n", "    \"\"\"\n", "    if not isinstance(text, str):\n", "        text = str(text)\n", "    \n", "    # Remove leading/trailing whitespace\n", "    text = text.strip()\n", "    \n", "    # Normalize case\n", "    if normalize_case:\n", "        text = text.lower()\n", "    \n", "    # Remove special characters\n", "    if remove_special_chars:\n", "        text = re.sub(r'[^\\w\\s]', ' ', text)\n", "    \n", "    # Remove numbers\n", "    if remove_numbers:\n", "        text = re.sub(r'\\d+', ' ', text)\n", "    \n", "    # Remove extra whitespace\n", "    if remove_extra_whitespace:\n", "        text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    # Filter words by minimum length\n", "    if min_word_length > 1:\n", "        words = text.split()\n", "        words = [word for word in words if len(word) >= min_word_length]\n", "        text = ' '.join(words)\n", "    \n", "    return text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Text Files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_text_files():\n", "    \"\"\"\n", "    Load text files from both directories.\n", "    \"\"\"\n", "    vector_db_path = \"1-Langchain/Vector_DB/cleaned_text.txt\"\n", "    vector_store_db_path = \"1-Langchain/VectorStoreDB/cleaned_text.txt\"\n", "    \n", "    vector_db_text = \"\"\n", "    vector_store_db_text = \"\"\n", "    \n", "    # Load Vector_DB text\n", "    try:\n", "        if os.path.exists(vector_db_path):\n", "            with open(vector_db_path, 'r', encoding='utf-8') as f:\n", "                vector_db_text = f.read()\n", "            print(f\"✅ Loaded Vector_DB text: {len(vector_db_text):,} characters\")\n", "        else:\n", "            print(f\"❌ Vector_DB file not found: {vector_db_path}\")\n", "    except Exception as e:\n", "        print(f\"❌ Error loading Vector_DB file: {e}\")\n", "    \n", "    # Load VectorStoreDB text\n", "    try:\n", "        if os.path.exists(vector_store_db_path):\n", "            with open(vector_store_db_path, 'r', encoding='utf-8') as f:\n", "                vector_store_db_text = f.read()\n", "            print(f\"✅ Loaded VectorStoreDB text: {len(vector_store_db_text):,} characters\")\n", "        else:\n", "            print(f\"❌ VectorStoreDB file not found: {vector_store_db_path}\")\n", "    except Exception as e:\n", "        print(f\"❌ Error loading VectorStoreDB file: {e}\")\n", "    \n", "    return vector_db_text, vector_store_db_text\n", "\n", "# Load the texts\n", "vector_db_text, vector_store_db_text = load_text_files()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Clean and Combine Texts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Clean both texts\n", "print(\"\\nCleaning texts...\")\n", "\n", "cleaned_vector_db = clean_text(\n", "    vector_db_text,\n", "    remove_extra_whitespace=True,\n", "    normalize_case=False,\n", "    remove_special_chars=False,\n", "    remove_numbers=False,\n", "    min_word_length=2\n", ")\n", "\n", "cleaned_vector_store_db = clean_text(\n", "    vector_store_db_text,\n", "    remove_extra_whitespace=True,\n", "    normalize_case=False,\n", "    remove_special_chars=False,\n", "    remove_numbers=False,\n", "    min_word_length=2\n", ")\n", "\n", "print(f\"Vector_DB cleaned: {len(cleaned_vector_db):,} characters\")\n", "print(f\"VectorStoreDB cleaned: {len(cleaned_vector_store_db):,} characters\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Combine the texts\n", "combined_text = f\"\"\"\n", "=== MEDICAL RESEARCH AND INFECTIOUS DISEASES ===\n", "{cleaned_vector_db}\n", "\n", "=== CARDIOVASCULAR EXERCISE AND PHYSICAL ACTIVITY ===\n", "{cleaned_vector_store_db}\n", "\"\"\".strip()\n", "\n", "print(f\"\\nCombined text length: {len(combined_text):,} characters\")\n", "print(f\"Combined text word count: {len(combined_text.split()):,} words\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Combined Text Files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the main combined file\n", "output_path = \"combined_cleaned_text.txt\"\n", "try:\n", "    with open(output_path, 'w', encoding='utf-8') as f:\n", "        f.write(combined_text)\n", "    print(f\"\\n✅ Combined text saved to: {output_path}\")\n", "except Exception as e:\n", "    print(f\"❌ Error saving combined text: {e}\")\n", "\n", "# Save simple version (no headers)\n", "simple_combined = f\"{cleaned_vector_db}\\n\\n{cleaned_vector_store_db}\"\n", "simple_output_path = \"simple_combined_text.txt\"\n", "try:\n", "    with open(simple_output_path, 'w', encoding='utf-8') as f:\n", "        f.write(simple_combined)\n", "    print(f\"✅ Simple combined text saved to: {simple_output_path}\")\n", "except Exception as e:\n", "    print(f\"❌ Error saving simple combined text: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Display Statistics and Previews"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display statistics\n", "print(\"\\n=== FINAL STATISTICS ===\")\n", "print(f\"Vector_DB original: {len(vector_db_text):,} characters\")\n", "print(f\"Vector_DB cleaned: {len(cleaned_vector_db):,} characters\")\n", "print(f\"VectorStoreDB original: {len(vector_store_db_text):,} characters\")\n", "print(f\"VectorStoreDB cleaned: {len(cleaned_vector_store_db):,} characters\")\n", "print(f\"Combined total: {len(combined_text):,} characters\")\n", "print(f\"Combined words: {len(combined_text.split()):,} words\")\n", "\n", "# Show content previews\n", "print(\"\\n=== CONTENT PREVIEWS ===\")\n", "print(\"\\nVector_DB (Medical Research):\")\n", "print(cleaned_vector_db[:300] + \"...\" if len(cleaned_vector_db) > 300 else cleaned_vector_db)\n", "\n", "print(\"\\nVectorStoreDB (Cardiovascular Exercise):\")\n", "print(cleaned_vector_store_db[:300] + \"...\" if len(cleaned_vector_store_db) > 300 else cleaned_vector_store_db)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Success Summary\n", "\n", "This notebook has successfully:\n", "\n", "1. **Applied compatibility fixes** for the ForwardRef error\n", "2. **Loaded both text files** from Vector_DB and VectorStoreDB\n", "3. **Cleaned the texts** using a unified function\n", "4. **Combined them** into single files\n", "5. **Saved multiple versions**:\n", "   - `combined_cleaned_text.txt` - With section headers\n", "   - `simple_combined_text.txt` - Simple concatenation\n", "\n", "The combined files are ready to use for your vector store or other applications!"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}