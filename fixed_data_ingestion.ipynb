# Import with compatibility handling
import pandas as pd
import os
import warnings
warnings.filterwarnings("ignore")

# Try different import paths for Document
try:
    from langchain_core.documents import Document
    print("✅ Imported Document from langchain_core")
except ImportError:
    try:
        from langchain.docstore.document import Document
        print("✅ Imported Document from langchain.docstore")
    except ImportError:
        from langchain.schema import Document
        print("✅ Imported Document from langchain.schema")

def csv_to_documents(filepath):
    """
    Convert CSV file to LangChain documents.
    
    Args:
        filepath (str): Path to CSV file
    
    Returns:
        list: List of Document objects
    """
    if not os.path.exists(filepath):
        print(f"⚠️ File not found: {filepath}")
        return []
    
    try:
        df = pd.read_csv(filepath)
        docs = []

        for _, row in df.iterrows():
            # Create a text blob from each row (excluding NaNs)
            content = "\n".join([f"{col}: {row[col]}" for col in df.columns if pd.notna(row[col])])
            docs.append(Document(page_content=content, metadata={"source": filepath}))
        
        print(f"✅ Loaded {len(docs)} documents from {filepath}")
        return docs
    
    except Exception as e:
        print(f"❌ Error loading {filepath}: {e}")
        return []

# Load text file with compatibility
def load_text_file(filepath):
    """
    Load text file as Document.
    
    Args:
        filepath (str): Path to text file
    
    Returns:
        list: List containing single Document object
    """
    if not os.path.exists(filepath):
        print(f"⚠️ File not found: {filepath}")
        return []
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        doc = Document(page_content=content, metadata={"source": filepath})
        print(f"✅ Loaded text file: {filepath} ({len(content)} characters)")
        return [doc]
    
    except Exception as e:
        print(f"❌ Error loading {filepath}: {e}")
        return []

# Load all documents
print("Loading documents...")

# 1. Load cleaned_text.txt
original_docs = load_text_file("./cleaned_text.txt")

# 2. Load CSVs and convert to documents
docs_symptoms = csv_to_documents("./Text_FIles/symptom_checker_docs/disease_symptom_list.csv")
docs_medqa = csv_to_documents("./Text_files/symptom_checker_docs/medical_qa.csv")
docs_openfda = csv_to_documents("./Text_files/symptom_checker_docs/openfda_drugs.csv")

# 3. Combine all documents
all_documents = original_docs + docs_symptoms + docs_medqa + docs_openfda

print(f"\n📊 Total documents loaded: {len(all_documents)}")
print(f"   - Original text: {len(original_docs)}")
print(f"   - Symptoms: {len(docs_symptoms)}")
print(f"   - Medical QA: {len(docs_medqa)}")
print(f"   - OpenFDA drugs: {len(docs_openfda)}")

# Import vector store and embeddings with compatibility
print("Importing vector store components...")

# Try different import paths for FAISS
try:
    from langchain_community.vectorstores import FAISS
    print("✅ Imported FAISS from langchain_community")
except ImportError:
    try:
        from langchain.vectorstores import FAISS
        print("✅ Imported FAISS from langchain")
    except ImportError as e:
        print(f"❌ Could not import FAISS: {e}")
        raise

# Try different import paths for OllamaEmbeddings
try:
    from langchain_ollama import OllamaEmbeddings
    print("✅ Imported OllamaEmbeddings from langchain_ollama")
except ImportError:
    try:
        from langchain.embeddings import OllamaEmbeddings
        print("✅ Imported OllamaEmbeddings from langchain.embeddings")
    except ImportError:
        try:
            from langchain_community.embeddings import OllamaEmbeddings
            print("✅ Imported OllamaEmbeddings from langchain_community")
        except ImportError as e:
            print(f"❌ Could not import OllamaEmbeddings: {e}")
            print("Please install langchain-ollama: pip install langchain-ollama")
            raise

# Create Qwen vector store
print("\nCreating Qwen vector store...")

try:
    # Initialize embedding
    embedding_qwen = OllamaEmbeddings(model="qwen2.5:1.5b")
    print("✅ Qwen embedding model initialized")
    
    # Create vector store
    vectordb_qwen = FAISS.from_documents(all_documents, embedding_qwen)
    
    # Create directory if it doesn't exist
    os.makedirs("./Vector_Store/embeddings", exist_ok=True)
    
    # Save vector store
    vectordb_qwen.save_local("./Vector_Store/embeddings/qwen")
    print("✅ Qwen vector store created and saved successfully")
    
except Exception as e:
    print(f"❌ Error creating Qwen vector store: {e}")
    print("Make sure Ollama is running and qwen2.5:1.5b model is available")
    print("Run: ollama pull qwen2.5:1.5b")

# Create DeepSeek vector store
print("\nCreating DeepSeek vector store...")

try:
    # Initialize embedding
    embedding_deepseek = OllamaEmbeddings(model="deepseek-r1:1.5b")
    print("✅ DeepSeek embedding model initialized")
    
    # Create vector store
    vectordb_deepseek = FAISS.from_documents(all_documents, embedding_deepseek)
    
    # Save vector store
    vectordb_deepseek.save_local("./Vector_Store/embeddings/deepseek")
    print("✅ DeepSeek vector store created and saved successfully")
    
except Exception as e:
    print(f"❌ Error creating DeepSeek vector store: {e}")
    print("Make sure Ollama is running and deepseek-r1:1.5b model is available")
    print("Run: ollama pull deepseek-r1:1.5b")

# Test the vector stores
print("\n🧪 Testing vector stores...")

test_query = "tuberculosis symptoms treatment"

# Test Qwen vector store
try:
    if 'vectordb_qwen' in locals():
        results_qwen = vectordb_qwen.similarity_search(test_query, k=2)
        print(f"\n✅ Qwen search results for '{test_query}':")
        for i, result in enumerate(results_qwen, 1):
            print(f"   {i}. {result.page_content[:100]}...")
except Exception as e:
    print(f"❌ Error testing Qwen vector store: {e}")

# Test DeepSeek vector store
try:
    if 'vectordb_deepseek' in locals():
        results_deepseek = vectordb_deepseek.similarity_search(test_query, k=2)
        print(f"\n✅ DeepSeek search results for '{test_query}':")
        for i, result in enumerate(results_deepseek, 1):
            print(f"   {i}. {result.page_content[:100]}...")
except Exception as e:
    print(f"❌ Error testing DeepSeek vector store: {e}")

print("\n🎉 Data ingestion completed!")