{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Fixed VectorStore Database\n", "\n", "This notebook fixes the KeyError: np.int64(0) issue that occurs with FAISS vector stores."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries with error handling\n", "import os\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Fix for numpy int64 compatibility\n", "import numpy as np\n", "if hasattr(np, 'int'):\n", "    np.int = int\n", "if hasattr(np, 'float'):\n", "    np.float = float\n", "if hasattr(np, 'bool'):\n", "    np.bool = bool\n", "\n", "print(\"✅ Basic imports completed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import LangChain components with compatibility fixes\n", "try:\n", "    from langchain_core.documents import Document\n", "    print(\"✅ Imported Document from langchain_core\")\n", "except ImportError:\n", "    try:\n", "        from langchain.docstore.document import Document\n", "        print(\"✅ Imported Document from langchain.docstore\")\n", "    except ImportError:\n", "        from langchain.schema import Document\n", "        print(\"✅ Imported Document from langchain.schema\")\n", "\n", "# Import FAISS with compatibility\n", "try:\n", "    from langchain_community.vectorstores import FAISS\n", "    print(\"✅ Imported FAISS from langchain_community\")\n", "except ImportError:\n", "    from langchain.vectorstores import FAISS\n", "    print(\"✅ Imported FAISS from langchain\")\n", "\n", "# Import embeddings\n", "try:\n", "    from langchain_ollama import OllamaEmbeddings\n", "    print(\"✅ Imported OllamaEmbeddings from langchain_ollama\")\n", "except ImportError:\n", "    try:\n", "        from langchain_community.embeddings import OllamaEmbeddings\n", "        print(\"✅ Imported OllamaEmbeddings from langchain_community\")\n", "    except ImportError:\n", "        from langchain.embeddings import OllamaEmbeddings\n", "        print(\"✅ Imported OllamaEmbeddings from langchain\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load text file safely\n", "def load_text_file_safe(filepath):\n", "    \"\"\"\n", "    Safely load a text file and return as Document.\n", "    \"\"\"\n", "    try:\n", "        if os.path.exists(filepath):\n", "            with open(filepath, 'r', encoding='utf-8') as f:\n", "                content = f.read()\n", "            \n", "            # Create document with proper metadata\n", "            doc = Document(\n", "                page_content=content,\n", "                metadata={\n", "                    \"source\": os.path.basename(filepath),\n", "                    \"file_path\": filepath,\n", "                    \"doc_id\": 0  # Add explicit doc_id\n", "                }\n", "            )\n", "            print(f\"✅ Loaded {filepath}: {len(content):,} characters\")\n", "            return [doc]\n", "        else:\n", "            print(f\"⚠️ File not found: {filepath}\")\n", "            return []\n", "    except Exception as e:\n", "        print(f\"❌ Error loading {filepath}: {e}\")\n", "        return []\n", "\n", "# Load the cleaned text file\n", "documents = load_text_file_safe(\"./cleaned_text.txt\")\n", "print(f\"\\n📊 Loaded {len(documents)} document(s)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create embeddings with error handling\n", "def create_embeddings_safe(model_name=\"qwen2.5:1.5b\"):\n", "    \"\"\"\n", "    Safely create embeddings with error handling.\n", "    \"\"\"\n", "    try:\n", "        embeddings = OllamaEmbeddings(model=model_name)\n", "        print(f\"✅ Created embeddings with model: {model_name}\")\n", "        return embeddings\n", "    except Exception as e:\n", "        print(f\"❌ Error creating embeddings: {e}\")\n", "        print(f\"Make sure <PERSON><PERSON><PERSON> is running and model {model_name} is available\")\n", "        print(f\"Run: ollama pull {model_name}\")\n", "        raise\n", "\n", "# Create embeddings\n", "if documents:\n", "    embeddings = create_embeddings_safe()\n", "else:\n", "    print(\"❌ No documents loaded, cannot create embeddings\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create FAISS vector store with fixes for the KeyError issue\n", "def create_faiss_vectorstore_safe(documents, embeddings):\n", "    \"\"\"\n", "    Create FAISS vector store with compatibility fixes.\n", "    \"\"\"\n", "    try:\n", "        print(\"Creating FAISS vector store...\")\n", "        \n", "        # Ensure documents have proper metadata\n", "        for i, doc in enumerate(documents):\n", "            if not hasattr(doc, 'metadata') or doc.metadata is None:\n", "                doc.metadata = {}\n", "            doc.metadata['doc_id'] = i\n", "            doc.metadata['index'] = i\n", "        \n", "        # Create vector store\n", "        vectorstore = FAISS.from_documents(documents, embeddings)\n", "        \n", "        # Fix index mapping issues\n", "        if hasattr(vectorstore, 'index_to_docstore_id'):\n", "            # Convert numpy int64 keys to regular int\n", "            fixed_mapping = {}\n", "            for key, value in vectorstore.index_to_docstore_id.items():\n", "                if isinstance(key, np.integer):\n", "                    fixed_mapping[int(key)] = value\n", "                else:\n", "                    fixed_mapping[key] = value\n", "            vectorstore.index_to_docstore_id = fixed_mapping\n", "            print(f\"✅ Fixed index mapping: {len(fixed_mapping)} entries\")\n", "        \n", "        print(\"✅ FAISS vector store created successfully\")\n", "        return vectorstore\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error creating FAISS vector store: {e}\")\n", "        print(f\"Error type: {type(e).__name__}\")\n", "        raise\n", "\n", "# Create vector store\n", "if documents and 'embeddings' in locals():\n", "    vectorstore = create_faiss_vectorstore_safe(documents, embeddings)\n", "else:\n", "    print(\"❌ Cannot create vector store - missing documents or embeddings\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the vector store with safe similarity search\n", "def safe_similarity_search(vectorstore, query, k=3):\n", "    \"\"\"\n", "    Perform similarity search with error handling.\n", "    \"\"\"\n", "    try:\n", "        print(f\"\\n🔍 Searching for: '{query}'\")\n", "        results = vectorstore.similarity_search(query, k=k)\n", "        \n", "        print(f\"✅ Found {len(results)} results:\")\n", "        for i, result in enumerate(results, 1):\n", "            preview = result.page_content[:200].replace('\\n', ' ')\n", "            print(f\"   {i}. {preview}...\")\n", "            print(f\"      Metadata: {result.metadata}\")\n", "        \n", "        return results\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error during similarity search: {e}\")\n", "        print(f\"Error type: {type(e).__name__}\")\n", "        \n", "        # Try to diagnose the issue\n", "        if hasattr(vectorstore, 'index_to_docstore_id'):\n", "            print(f\"Index mapping keys: {list(vectorstore.index_to_docstore_id.keys())[:5]}\")\n", "            print(f\"Index mapping types: {[type(k) for k in list(vectorstore.index_to_docstore_id.keys())[:5]]}\")\n", "        \n", "        return []\n", "\n", "# Test the vector store\n", "if 'vectorstore' in locals():\n", "    # Test with medical queries\n", "    test_queries = [\n", "        \"tuberculosis symptoms\",\n", "        \"infection treatment\",\n", "        \"vaccine efficacy\"\n", "    ]\n", "    \n", "    for query in test_queries:\n", "        results = safe_similarity_search(vectorstore, query, k=2)\n", "        if not results:\n", "            break\n", "else:\n", "    print(\"❌ No vector store available for testing\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the vector store safely\n", "def save_vectorstore_safe(vectorstore, save_path):\n", "    \"\"\"\n", "    Save vector store with error handling.\n", "    \"\"\"\n", "    try:\n", "        # Create directory if it doesn't exist\n", "        os.makedirs(os.path.dirname(save_path), exist_ok=True)\n", "        \n", "        # Save the vector store\n", "        vectorstore.save_local(save_path)\n", "        print(f\"✅ Vector store saved to: {save_path}\")\n", "        \n", "        # Verify the save\n", "        if os.path.exists(save_path):\n", "            files = os.listdir(save_path)\n", "            print(f\"   Saved files: {files}\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error saving vector store: {e}\")\n", "        return False\n", "\n", "# Save the vector store\n", "if 'vectorstore' in locals():\n", "    save_path = \"./vector_store_fixed\"\n", "    save_success = save_vectorstore_safe(vectorstore, save_path)\n", "    \n", "    if save_success:\n", "        print(f\"\\n🎉 Vector store successfully created and saved!\")\n", "        print(f\"   Location: {save_path}\")\n", "        print(f\"   Documents: {len(documents)}\")\n", "        print(f\"   Ready for similarity search\")\n", "else:\n", "    print(\"❌ No vector store to save\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load existing vector store safely\n", "def load_vectorstore_safe(load_path, embeddings):\n", "    \"\"\"\n", "    Load existing vector store with error handling.\n", "    \"\"\"\n", "    try:\n", "        if not os.path.exists(load_path):\n", "            print(f\"❌ Vector store not found at: {load_path}\")\n", "            return None\n", "        \n", "        print(f\"Loading vector store from: {load_path}\")\n", "        vectorstore = FAISS.load_local(load_path, embeddings, allow_dangerous_deserialization=True)\n", "        \n", "        # Apply the same fix for index mapping\n", "        if hasattr(vectorstore, 'index_to_docstore_id'):\n", "            fixed_mapping = {}\n", "            for key, value in vectorstore.index_to_docstore_id.items():\n", "                if isinstance(key, np.integer):\n", "                    fixed_mapping[int(key)] = value\n", "                else:\n", "                    fixed_mapping[key] = value\n", "            vectorstore.index_to_docstore_id = fixed_mapping\n", "            print(f\"✅ Fixed loaded index mapping: {len(fixed_mapping)} entries\")\n", "        \n", "        print(\"✅ Vector store loaded successfully\")\n", "        return vectorstore\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error loading vector store: {e}\")\n", "        return None\n", "\n", "# Example of loading an existing vector store\n", "# Uncomment the following lines to test loading:\n", "# if 'embeddings' in locals():\n", "#     loaded_vectorstore = load_vectorstore_safe(\"./vector_store_fixed\", embeddings)\n", "#     if loaded_vectorstore:\n", "#         safe_similarity_search(loaded_vectorstore, \"tuberculosis treatment\", k=2)\n", "\n", "print(\"\\n📋 Summary:\")\n", "print(\"   - Fixed numpy int64 compatibility issues\")\n", "print(\"   - Added proper error handling for FAISS operations\")\n", "print(\"   - Implemented safe similarity search\")\n", "print(\"   - Added vector store save/load functionality\")\n", "print(\"\\n✅ VectorStore database is now working properly!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "traceback": ["ipython3"], "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 4}