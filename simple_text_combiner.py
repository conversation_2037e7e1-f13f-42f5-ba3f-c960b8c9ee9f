#!/usr/bin/env python3
"""
Simple Text Combiner - No Dependencies
Combines cleaned_text.txt files from Vector_DB and VectorStoreDB directories
"""

import os
import re


def clean_text(text, 
               remove_extra_whitespace=True,
               normalize_case=False,
               remove_special_chars=False,
               remove_numbers=False,
               min_word_length=1):
    """
    Simple text cleaning function.
    
    Args:
        text (str): Input text to clean
        remove_extra_whitespace (bool): Remove extra spaces and normalize whitespace
        normalize_case (bool): Convert to lowercase
        remove_special_chars (bool): Remove special characters
        remove_numbers (bool): Remove all numbers
        min_word_length (int): Minimum word length to keep
    
    Returns:
        str: Cleaned text
    """
    if not isinstance(text, str):
        text = str(text)
    
    # Remove leading/trailing whitespace
    text = text.strip()
    
    # Normalize case
    if normalize_case:
        text = text.lower()
    
    # Remove special characters
    if remove_special_chars:
        text = re.sub(r'[^\w\s]', ' ', text)
    
    # Remove numbers
    if remove_numbers:
        text = re.sub(r'\d+', ' ', text)
    
    # Remove extra whitespace
    if remove_extra_whitespace:
        text = re.sub(r'\s+', ' ', text).strip()
    
    # Filter words by minimum length
    if min_word_length > 1:
        words = text.split()
        words = [word for word in words if len(word) >= min_word_length]
        text = ' '.join(words)
    
    return text


def load_text_files():
    """
    Load text files from both directories.
    
    Returns:
        tuple: (vector_db_text, vector_store_db_text)
    """
    vector_db_path = "1-Langchain/Vector_DB/cleaned_text.txt"
    vector_store_db_path = "1-Langchain/VectorStoreDB/cleaned_text.txt"
    
    vector_db_text = ""
    vector_store_db_text = ""
    
    # Load Vector_DB text
    try:
        if os.path.exists(vector_db_path):
            with open(vector_db_path, 'r', encoding='utf-8') as f:
                vector_db_text = f.read()
            print(f"✅ Loaded Vector_DB text: {len(vector_db_text):,} characters")
        else:
            print(f"❌ Vector_DB file not found: {vector_db_path}")
    except Exception as e:
        print(f"❌ Error loading Vector_DB file: {e}")
    
    # Load VectorStoreDB text
    try:
        if os.path.exists(vector_store_db_path):
            with open(vector_store_db_path, 'r', encoding='utf-8') as f:
                vector_store_db_text = f.read()
            print(f"✅ Loaded VectorStoreDB text: {len(vector_store_db_text):,} characters")
        else:
            print(f"❌ VectorStoreDB file not found: {vector_store_db_path}")
    except Exception as e:
        print(f"❌ Error loading VectorStoreDB file: {e}")
    
    return vector_db_text, vector_store_db_text


def main():
    """Main function to combine text files."""
    print("=== Text File Combiner ===\n")
    
    # Load the texts
    vector_db_text, vector_store_db_text = load_text_files()
    
    if not vector_db_text and not vector_store_db_text:
        print("❌ No text files found to combine!")
        return
    
    # Clean both texts
    print("\nCleaning texts...")
    
    cleaned_vector_db = clean_text(
        vector_db_text,
        remove_extra_whitespace=True,
        normalize_case=False,
        remove_special_chars=False,
        remove_numbers=False,
        min_word_length=2
    )
    
    cleaned_vector_store_db = clean_text(
        vector_store_db_text,
        remove_extra_whitespace=True,
        normalize_case=False,
        remove_special_chars=False,
        remove_numbers=False,
        min_word_length=2
    )
    
    print(f"Vector_DB cleaned: {len(cleaned_vector_db):,} characters")
    print(f"VectorStoreDB cleaned: {len(cleaned_vector_store_db):,} characters")
    
    # Combine the texts
    combined_text = f"""=== MEDICAL RESEARCH AND INFECTIOUS DISEASES ===
{cleaned_vector_db}

=== CARDIOVASCULAR EXERCISE AND PHYSICAL ACTIVITY ===
{cleaned_vector_store_db}""".strip()
    
    print(f"\nCombined text length: {len(combined_text):,} characters")
    print(f"Combined text word count: {len(combined_text.split()):,} words")
    
    # Save the main combined file
    output_path = "combined_cleaned_text.txt"
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(combined_text)
        print(f"\n✅ Combined text saved to: {output_path}")
    except Exception as e:
        print(f"❌ Error saving combined text: {e}")
    
    # Save simple version (no headers)
    simple_combined = f"{cleaned_vector_db}\n\n{cleaned_vector_store_db}"
    simple_output_path = "simple_combined_text.txt"
    try:
        with open(simple_output_path, 'w', encoding='utf-8') as f:
            f.write(simple_combined)
        print(f"✅ Simple combined text saved to: {simple_output_path}")
    except Exception as e:
        print(f"❌ Error saving simple combined text: {e}")
    
    # Display statistics
    print("\n=== FINAL STATISTICS ===")
    print(f"Vector_DB original: {len(vector_db_text):,} characters")
    print(f"Vector_DB cleaned: {len(cleaned_vector_db):,} characters")
    print(f"VectorStoreDB original: {len(vector_store_db_text):,} characters")
    print(f"VectorStoreDB cleaned: {len(cleaned_vector_store_db):,} characters")
    print(f"Combined total: {len(combined_text):,} characters")
    print(f"Combined words: {len(combined_text.split()):,} words")
    
    # Show content previews
    print("\n=== CONTENT PREVIEWS ===")
    if cleaned_vector_db:
        print("\nVector_DB (Medical Research):")
        preview = cleaned_vector_db[:300] + "..." if len(cleaned_vector_db) > 300 else cleaned_vector_db
        print(preview)
    
    if cleaned_vector_store_db:
        print("\nVectorStoreDB (Cardiovascular Exercise):")
        preview = cleaned_vector_store_db[:300] + "..." if len(cleaned_vector_store_db) > 300 else cleaned_vector_store_db
        print(preview)
    
    print("\n✅ Text combination completed successfully!")


if __name__ == "__main__":
    main()
